# 领料弹窗请求优化测试

## 测试步骤

### 1. 打开浏览器开发者工具
- 按 F12 打开开发者工具
- 切换到 Network（网络）标签页
- 清空网络请求记录

### 2. 第一次打开领料弹窗
- 在工作订单页面点击"领料"按钮
- 观察网络请求，应该看到以下请求：
  - `getWarehouseList` - 获取仓库列表
  - `getWarehouseLocationPage` - 获取库位列表  
  - `getUnitPage` - 获取单位列表
  - `getWorkOrderDetailListByBizOrderId` - 获取工单明细

### 3. 关闭弹窗后再次打开
- 关闭领料弹窗
- 再次点击"领料"按钮
- 观察网络请求，应该只看到：
  - `getWorkOrderDetailListByBizOrderId` - 获取工单明细
- **不应该再看到** `getWarehouseList`、`getWarehouseLocationPage`、`getUnitPage` 请求

### 4. 测试防抖机制
- 快速连续点击"领料"按钮多次
- 应该只打开一个弹窗，不会有重复请求

## 预期结果

### 优化前
- 每次打开弹窗都会发送 4-6 个请求
- 快速点击会导致多个弹窗和重复请求

### 优化后
- 第一次打开：发送必要的初始化请求
- 后续打开：只发送业务相关请求，基础数据使用缓存
- 快速点击：防抖机制阻止重复操作

## 验证缓存机制

可以在浏览器控制台中执行以下代码来检查缓存状态：

```javascript
// 检查全局缓存
console.log('缓存状态:', window.pickingReceiptDetailFormCache)

// 检查各项数据是否已缓存
const cache = window.pickingReceiptDetailFormCache
if (cache) {
  console.log('库位数据已缓存:', cache.isLocationListLoaded, '数量:', cache.locationList.length)
  console.log('单位数据已缓存:', cache.isUnitsLoaded, '数量:', cache.unitList.length)
  console.log('仓库数据已缓存:', cache.isWarehouseListLoaded, '数量:', cache.warehouseList.length)
}
```

## 注意事项

1. **页面刷新后缓存会清空** - 这是正常的，因为缓存存储在 window 对象中
2. **如果数据有更新** - 可能需要手动清空缓存或刷新页面
3. **内存使用** - 缓存会占用一定内存，但对于这些基础数据来说影响很小

## 清空缓存（如需要）

如果需要清空缓存强制重新加载数据，可以在控制台执行：

```javascript
// 方法1：使用工具函数清空所有相关缓存（推荐）
import { clearCommonBizCache } from '@/utils/commonBiz'
clearCommonBizCache()

// 方法2：手动清空各个缓存
delete window.pickingReceiptDetailFormCache
delete window.commonBizUnitCache
delete window.commonBizWarehouseCache
console.log('所有缓存已清空')
```

## 缓存机制说明

### 1. 组件级缓存
- `pickingReceiptDetailFormCache` - 存储领料详情表单的基础数据缓存

### 2. 工具函数级缓存
- `commonBizUnitCache` - 存储单位数据缓存
- `commonBizWarehouseCache` - 存储仓库数据缓存

### 3. 缓存策略
- **Promise 缓存**：防止并发请求时的重复调用
- **数据缓存**：成功加载后缓存结果数据
- **错误处理**：请求失败时清理 Promise 缓存，允许重试
