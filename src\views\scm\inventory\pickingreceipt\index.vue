<template>
  <!-- 列表 -->
  <ContentWrap>
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="待领料" name="pendingPicking" >
        <el-table
          v-loading="workOrderLoading"
          :data="flattenedWorkOrderList"
          :stripe="true"
          border
          :show-overflow-tooltip="true"
          highlight-current-row
          show-summary
          :summary-method="workOrderSummaryMethod"
          @selection-change="handleWorkOrderSelectionChange"
          :span-method="workOrderSpanMethod"
          height="600"
          style="width: 100%"
        >
          <!-- <el-table-column type="selection" width="60" fixed="left" /> -->

          <el-table-column label="生产单号" align="left" prop="workNo" width="140" fixed="left">
            <template #default="scope">
              <div class="work-no-container">
                <div class="order-no-cell">
                  <div class="order-no-content">
                    <span class="work-no-text">{{ scope.row.workNo }}</span>
                  </div>
                  <el-button
                    link
                    type="info"
                    @click="copyOrderNo(scope.row.workNo)"
                    class="copy-btn copy-btn-fixed"
                    size="small"
                  >
                    <Icon icon="ep:copy-document" :size="12"/>
                  </el-button>
                </div>
                <dict-tag
                  v-if="scope.row.approveStatus"
                  :type="DICT_TYPE.APPROVE_STATUS"
                  :value="scope.row.approveStatus"
                  class="status-tag"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="产品编号" align="center" prop="productCode" width="120" fixed="left"/>
          <el-table-column label="产品名称" align="center" prop="productName" width="140" fixed="left"/>
          <el-table-column label="规格" align="center" prop="spec" />
          <el-table-column label="订单数量" align="center" prop="orderQuantity" width="110px">
            <template #default="scope">
              <el-tag>{{ scope.row.orderQuantity || 0 }} {{ getUnitName(scope.row.orderUnit) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="计划数量" align="center" prop="scheduleQuantity" width="110">
            <template  #default="scope">
              <el-tag>
                {{ scope.row.scheduleQuantity || 0 }} {{ getUnitName(scope.row.orderUnit) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="计划件数" align="center" prop="schedulePiece" width="100"/>

          <!-- 明细信息 - 放在开始时间前面 -->
          <el-table-column label="仓库" align="center" width="120px">
            <template #default="scope">
              {{ getWorkOrderWarehouseName(scope.row.detail?.warehouseId) || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="物料名称" align="left" prop="detail.materialName" min-width="180px"/>
          <el-table-column label="物料编号" align="center" prop="detail.materialCode" min-width="120px"/>
          <el-table-column label="规格" align="center" prop="detail.spec" min-width="100px"/>
          <el-table-column label="单位" align="center" width="80px">
            <template #default="scope">
              {{ getWorkOrderUnitName(scope.row.detail?.unit) || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="计划数量" align="center" prop="detail.plannedQuantity" width="100px">
            <template #default="scope">
              {{ formatQuantity(scope.row.detail?.plannedQuantity) }}
            </template>
          </el-table-column>
          <el-table-column label="履约数量" align="center" prop="detail.fulfilledQuantity" width="100px">
            <template #default="scope">
              {{ formatQuantity(scope.row.detail?.fulfilledQuantity) }}
            </template>
          </el-table-column>
          <el-table-column label="批号" align="center" prop="detail.batchNo" width="120px"/>


          <el-table-column
            label="开始时间"
            align="center"
            prop="scheduleStartTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column
            label="结束时间"
            align="center"
            prop="scheduleEndTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column label="来源类型" align="center" prop="orderType" width="120">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.MFG_ORDER_SOURCE" :value="scope.row.orderType" />
            </template>
          </el-table-column>
          <el-table-column label="来源单号" align="center" prop="orderNo" width="120">
            <template #default="scope">
              <div class="order-no-cell" v-if="scope.row.orderNo">
                <div class="order-no-content">
                  <span>{{ scope.row.orderNo }}</span>
                </div>
                <el-button
                  link
                  type="info"
                  @click="copyOrderNo(scope.row.orderNo)"
                  class="copy-btn copy-btn-fixed"
                  size="small"
                >
                  <Icon icon="ep:copy-document" :size="12"/>
                </el-button>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="bom" align="center" prop="bomCode">
            <template #default="scope">
              <el-tag v-if="scope.row.bomCode">{{ scope.row.bomCode || '无' }} {{ scope.row.bomVersion }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.WORK_ORDER_STATUS" :value="scope.row.status" />
            </template>
          </el-table-column>

          <el-table-column label="领料状态" align="center" prop="pickingStatus" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.COMMON_TASK_STATUS" :value="scope.row.pickingStatus" />
            </template>
          </el-table-column>
          <el-table-column label="入库状态" align="center" prop="inStockStatus" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.COMMON_TASK_STATUS" :value="scope.row.inStockStatus" />
            </template>
          </el-table-column>
          <el-table-column label="报工状态" align="center" prop="reportStatus" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.COMMON_TASK_STATUS" :value="scope.row.reportStatus" />
            </template>
          </el-table-column>
          <el-table-column label="质检状态" align="center" prop="qualityStatus" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.COMMON_TASK_STATUS" :value="scope.row.qualityStatus" />
            </template>
          </el-table-column>
          <el-table-column
            label="下单时间"
            align="center"
            prop="orderDate"
            :formatter="dateFormatter2"
            width="100px"
          />
          <el-table-column
            label="交期"
            align="center"
            prop="deliverDate"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column label="计划用时" align="center" prop="scheduleCostTime" width="100"/>
          <el-table-column label="计划产线" align="center" prop="scheduleLine" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.MANUFACTURE_LINE" :value="scope.row.scheduleLine" />
            </template>
          </el-table-column>
          <el-table-column label="计划用人" align="center" prop="scheduleHeadcount" width="100"/>
          <el-table-column label="生产要求" align="center" prop="requirement" width="100"/>
          <el-table-column label="备注" align="center" prop="remark" />
          <el-table-column label="完成进度" align="center" prop="progress" width="100"/>
          <el-table-column label="实际生产线" align="center" prop="actualLine" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.MANUFACTURE_LINE" :value="scope.row.actualLine" />
            </template>
          </el-table-column>
          <el-table-column label="实际生产数量" align="center" prop="actualQuantity" width="120"/>
          <el-table-column
            label="实际开始时间"
            align="center"
            prop="actualStartTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column
            label="实际结束时间"
            align="center"
            prop="actualEndTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column label="实际耗时" align="center" prop="actualCostTime" width="100"/>
          <el-table-column label="实际用人" align="center" prop="actualHeadcount" width="100"/>
          <el-table-column label="实际生产件数" align="center" prop="actualPiece" width="120"/>
          <el-table-column label="批号" align="center" prop="actualBatchNo" />
          <el-table-column label="生产备注" align="center" prop="actualRemark" width="100"/>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column label="审批单号" align="center" prop="approveNo" width="120"/>
          <el-table-column
            label="审批时间"
            align="center"
            prop="approvalTime"
            :formatter="dateFormatter"
            width="180px"
          />

          <el-table-column label="操作" align="center" min-width="100px" fixed="right">
            <template #default="scope">
              <el-button
                link
                type="success"
                @click="openPickingForm('create', scope.row)"
                v-hasPermi="['mfg:work-order:picking']"
              >
                领料
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="workOrderTotal"
          v-model:page="workOrderQueryParams.pageNo"
          v-model:limit="workOrderQueryParams.pageSize"
          @pagination="getWorkOrderList"
        />
        </el-tab-pane>
        <el-tab-pane label="已领料" name="picked">
          <el-card>
            <!-- 搜索工作栏 -->
            <el-form
              class="-mb-15px"
              :model="queryParams"
              ref="queryFormRef"
              :inline="true"
              label-width="auto"
            >
              <!-- 基础搜索项 - 始终显示 -->
              <el-form-item label="单号" prop="orderNo">
                <el-input
                  v-model="queryParams.orderNo"
                  placeholder="请输入单号"
                  clearable
                  @keyup.enter="handleQuery"
                  class="!w-240px"
                />
              </el-form-item>
              <el-form-item label="来源类型" prop="sourceType">
                <el-select
                  v-model="queryParams.sourceType"
                  placeholder="请选择来源类型"
                  clearable
                  class="!w-240px"
                >
                  <el-option
                  v-for="item in material_source"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="交易对象名称" prop="objectName">
                <el-input
                  v-model="queryParams.objectName"
                  placeholder="请输入交易对象名称"
                  clearable
                  @keyup.enter="handleQuery"
                  class="!w-240px"
                />
              </el-form-item>

              <!-- 高级搜索项 - 可展开收起 -->
              <template v-if="isExpanded">
                <el-form-item label="业务类型" prop="bizType">
                  <el-select
                    v-model="queryParams.bizType"
                    placeholder="请选择业务类型"
                    clearable
                    class="!w-240px"
                  >
                    <el-option
                      v-for="item in inventory_transaction_type"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="来源单编号" prop="sourceNo">
                  <el-input
                    v-model="queryParams.sourceNo"
                    placeholder="请输入来源单编号"
                    clearable
                    @keyup.enter="handleQuery"
                    class="!w-240px"
                  />
                </el-form-item>
                <el-form-item label="交易对象订单号" prop="objectOrderNo">
                  <el-input
                    v-model="queryParams.objectOrderNo"
                    placeholder="请输入交易对象订单号"
                    clearable
                    @keyup.enter="handleQuery"
                    class="!w-240px"
                  />
                </el-form-item>
                <el-form-item label="交易日期" prop="date">
                  <el-date-picker
                    v-model="queryParams.date"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    type="daterange"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                    class="!w-240px"
                  />
                </el-form-item>
                <el-form-item label="创建时间" prop="createTime">
                  <el-date-picker
                    v-model="queryParams.createTime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    type="daterange"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                    class="!w-240px"
                  />
                </el-form-item>
                <el-form-item label="审批状态" prop="approveStatus">
                  <el-select
                    v-model="queryParams.approveStatus"
                    placeholder="请选择审批状态"
                    clearable
                    class="!w-240px"
                  >
                    <el-option
                    v-for="item in approve_status"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="审批单号" prop="approveNo">
                  <el-input
                    v-model="queryParams.approveNo"
                    placeholder="请输入审批单号"
                    clearable
                    @keyup.enter="handleQuery"
                    class="!w-240px"
                  />
                </el-form-item>
                <el-form-item label="审批人" prop="approverName">
                  <el-input
                    v-model="queryParams.approverName"
                    placeholder="请输入审批人"
                    clearable
                    @keyup.enter="handleQuery"
                    class="!w-240px"
                  />
                </el-form-item>
                <el-form-item label="审批时间" prop="approveDate">
                  <el-date-picker
                    v-model="queryParams.approveDate"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    type="daterange"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                    class="!w-240px"
                  />
                </el-form-item>
              </template>

              <!-- 操作按钮行 -->
              <el-form-item>
                <el-button @click="handleQuery" class="ml-4"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
                <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
                <el-button
                  type="primary"
                  plain
                  @click="openForm('create')"
                  v-hasPermi="['inventory:picking-receipt:create']"
                >
                  <Icon icon="ep:plus" class="mr-5px" /> 新增
                </el-button>
                <el-button
                  type="success"
                  plain
                  @click="handleExport"
                  :loading="exportLoading"
                  v-hasPermi="['inventory:picking-receipt:export']"
                >
                  <Icon icon="ep:download" class="mr-5px" /> 导出
                </el-button>
                <el-button
                  type="primary"
                  plain
                  @click="handleApproval"
                  :loading="exportLoading"
                  v-hasPermi="['inventory:picking-receipt:approve']"
                >
                  <Icon icon="ep:check" class="mr-5px" /> 审核
                </el-button>
                <el-button
                  type="text"
                  @click="toggleExpanded"
                  class="ml-2"
                >
                  {{ isExpanded ? '收起' : '展开' }}
                  <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-1" />
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        <div style="margin-top: 16px;">
          <el-table
          v-loading="loading"
          :data="pickedList"
          :stripe="true"
          border
          :show-overflow-tooltip="true"
          highlight-current-row
          show-summary
          :summary-method="summaryMethod"
          @current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
          :span-method="objectSpanMethod"
          height="600"
          style="width: 100%"
        >
          <el-table-column type="selection" width="60" fixed="left" />
          <!-- 最重要的主单据信息 -->
          <el-table-column label="单号" align="left" prop="orderNo" width="180px" fixed="left">
            <template #default="scope">
              <div class="order-no-container">
                <div class="order-no-cell">
                  <div class="order-no-content">
                    <el-link
                      type="primary"
                      @click="handleViewDetail(scope.row.id)"
                      :underline="false"
                      class="order-link"
                    >
                      {{ scope.row.orderNo }}
                    </el-link>
                  </div>
                  <el-button
                    link
                    type="info"
                    @click="copyOrderNo(scope.row.orderNo)"
                    class="copy-btn copy-btn-fixed"
                    size="small"
                  >
                    <Icon icon="ep:copy-document" :size="12"/>
                  </el-button>
                </div>
                <dict-tag
                  v-if="scope.row.approveStatus"
                  :type="DICT_TYPE.APPROVE_STATUS"
                  :value="scope.row.approveStatus"
                  class="status-tag"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="业务类型" align="left" prop="bizType" width="100px" fixed="left">
            <template #default="scope">
              {{ getDictLabel(DICT_TYPE.INVENTORY_TRANSACTION_TYPE, scope.row.bizType) }}
            </template>
          </el-table-column>

          <!-- 核心明细信息 - 最重要的业务数据 -->
          <el-table-column label="物料名称" align="left" prop="detail.materialName" width="180px" fixed="left"/>
          <el-table-column label="物料编号" align="left" prop="detail.materialCode" width="120px" />
          <el-table-column label="实领数量" align="right" prop="detail.fulfilledQuantity" width="120px">
            <template #default="scope">
              {{ formatQuantity(scope.row.detail.fulfilledQuantity) }}{{ getUnitName(scope.row.detail?.unit) || scope.row.detail?.unit || '' }}
            </template>
          </el-table-column>
          <el-table-column label="应领数量" align="right" prop="detail.plannedQuantity" width="120px">
            <template #default="scope">
              {{ formatQuantity(scope.row.detail.plannedQuantity) }}{{ getUnitName(scope.row.detail?.unit) || scope.row.detail?.unit || '' }}
            </template>
          </el-table-column>
          <!-- <el-table-column label="单位" align="center" width="80px">
            <template #default="scope">

            </template>
          </el-table-column> -->
          <el-table-column label="单价" align="right" prop="detail.unitPrice" width="100px">
            <template #default="scope">
              {{ formatAmount(scope.row.detail.unitPrice) }}
            </template>
          </el-table-column>
          <el-table-column label="金额" align="right" prop="detail.amount" width="100px">
            <template #default="scope">
              {{ formatAmount(scope.row.detail.amount) }}
            </template>
          </el-table-column>
          <el-table-column label="批号" align="left" prop="detail.batchNo" width="100px"/>



          <!-- 重要的主单据业务信息 -->
          <el-table-column label="来源类型" align="center" prop="sourceType" width="100px">
            <template #default="scope">
              {{ getDictLabel(DICT_TYPE.SCM_BIZ_TYPE, scope.row.sourceType) }}
            </template>
          </el-table-column>
          <el-table-column label="来源单编号" align="left" prop="sourceNo" width="120px">
            <template #default="scope">
              <div class="order-no-cell" v-if="scope.row.sourceNo">
                <div class="order-no-content">
                  <span>{{ scope.row.sourceNo }}</span>
                </div>
                <el-button
                  link
                  type="info"
                  @click="copyOrderNo(scope.row.sourceNo)"
                  class="copy-btn copy-btn-fixed"
                  size="small"
                >
                  <Icon icon="ep:copy-document" :size="12"/>
                </el-button>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="交易对象名称" align="left" prop="objectName" width="150px"/>
          <el-table-column
            label="交易日期"
            align="center"
            prop="date"
            :formatter="dateFormatter"
            width="100px"
          />


          <!-- 税务和财务信息 -->
          <el-table-column label="含税单价" align="right" prop="detail.taxPrice" width="100px">
            <template #default="scope">
              {{ formatAmount(scope.row.detail.taxPrice) }}
            </template>
          </el-table-column>
          <el-table-column label="含税金额" align="right" prop="detail.taxAmount" width="110px">
            <template #default="scope">
              {{ formatAmount(scope.row.detail.taxAmount) }}
            </template>
          </el-table-column>

          <!-- 基本单位信息 -->
          <!-- <el-table-column label="基本单位" align="center" width="100px">
            <template #default="scope">
              {{ getUnitName(scope.row.detail?.standardUnit) || scope.row.detail?.standardUnit || '' }}
            </template>
          </el-table-column>
          <el-table-column label="基本单位实领数量" align="right" prop="detail.standardFulfilledQuantity" width="140px">
            <template #default="scope">
              {{ formatQuantity(scope.row.detail.standardFulfilledQuantity) }}
            </template>
          </el-table-column>
          <el-table-column label="基本单位应领数量" align="right" prop="detail.standardPlannedQuantity" width="140px">
            <template #default="scope">
              {{ formatQuantity(scope.row.detail.standardPlannedQuantity) }}
            </template>
          </el-table-column> -->

          <!-- 开票信息 -->
          <!-- <el-table-column label="开票数量" align="right" prop="detail.invoiceQuantity" width="100px">
            <template #default="scope">
              {{ formatQuantity(scope.row.detail.invoiceQuantity) }}
            </template>
          </el-table-column>
          <el-table-column label="开票金额" align="right" prop="detail.invoiceAmount" width="100px">
            <template #default="scope">
              {{ formatAmount(scope.row.detail.invoiceAmount) }}
            </template>
          </el-table-column> -->

          <!-- 日期信息 -->
          <el-table-column
            label="生产日期"
            align="center"
            prop="detail.effictiveDate"
            :formatter="dateFormatter"
            width="100px"
          />
          <el-table-column
            label="失效日期"
            align="center"
            prop="detail.expiryDate"
            :formatter="dateFormatter"
            width="100px"
          />

          <!-- 其他主单据信息 -->
          <el-table-column label="交易对象订单号" align="left" prop="objectOrderNo" width="140px">
            <template #default="scope">
              <div class="order-no-cell" v-if="scope.row.objectOrderNo">
                <div class="order-no-content">
                  <span>{{ scope.row.objectOrderNo }}</span>
                </div>
                <el-button
                  link
                  type="info"
                  @click="copyOrderNo(scope.row.objectOrderNo)"
                  class="copy-btn copy-btn-fixed"
                  size="small"
                >
                  <Icon icon="ep:copy-document" :size="12"/>
                </el-button>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="摘要" align="left" prop="note" width="120px"/>
          <el-table-column label="备注" align="left" prop="remark" width="120px"/>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            :formatter="dateFormatter"
            width="180px"
          />

          <!-- 详细的明细信息 -->
          <!-- <el-table-column label="开票基本数量" align="right" prop="detail.standardInvoiceQuantity" width="120px"/> -->
          <el-table-column label="明细备注" align="left" prop="detail.remark" width="120px"/>
          <el-table-column label="说明" align="left" prop="detail.note" width="120px"/>

          <!-- 系统和标识信息 -->
          <!-- <el-table-column label="明细ID" align="center" prop="detail.id" width="80px" /> -->
          <!-- <el-table-column label="明细单号" align="left" prop="detail.bizOrderNo" width="120px" /> -->
          <!-- <el-table-column label="仓库ID" align="center" prop="detail.warehouseId" width="80px" /> -->
          <!-- <el-table-column label="库位ID" align="center" prop="detail.locationId" width="80px" /> -->
          <!-- <el-table-column label="物料ID" align="center" prop="detail.materialId" width="80px" /> -->
          <!-- <el-table-column label="源单ID" align="center" prop="detail.sourceId" width="80px"/> -->
          <el-table-column label="源单单号" align="left" prop="detail.sourceNo" width="120px">
            <template #default="scope">
              <div class="order-no-cell" v-if="scope.row.detail.sourceNo">
                <div class="order-no-content">
                  <span>{{ scope.row.detail.sourceNo }}</span>
                </div>
                <el-button
                  link
                  type="info"
                  @click="copyOrderNo(scope.row.detail.sourceNo)"
                  class="copy-btn copy-btn-fixed"
                  size="small"
                >
                  <Icon icon="ep:copy-document" :size="12"/>
                </el-button>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="成本对象编码" align="left" prop="detail.costObjectId" width="120px"/>
          <el-table-column label="成本对象名称" align="left" prop="detail.costObjectName" width="120px"/>
          <el-table-column label="记账凭证号" align="left" prop="detail.accountingVoucherNumber" width="120px"/> -->
          <el-table-column
            label="明细创建时间"
            align="center"
            prop="detail.createTime"
            :formatter="dateFormatter"
            width="180px"
          />
          <el-table-column label="操作" align="center" min-width="120px" fixed="right">
            <template #default="scope">
              <el-button
                link
                type="primary"
                @click="openForm('update', scope.row.id)"
                v-hasPermi="['inventory:picking-receipt:update']"
                v-if="scope.row.approveStatus !== '3'"
              >
                编辑
              </el-button>
              <el-button
                link
                type="danger"
                @click="handleDelete(scope.row.id)"
                v-hasPermi="['inventory:picking-receipt:delete']"
                v-if="scope.row.approveStatus !== '3'"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <Pagination
          :total="pickedTotal"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
        </div>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <PickingReceiptForm ref="formRef" @success="getList" />
  <!-- 工单领料表单 -->
  <PickingReceiptForm ref="pickingFormRef" @success="getList" />
  <!-- 审核弹窗 -->
  <ApproveInfoForm ref="approveInfoFormRef" @success="getList" :biz-id="currentRow?.id" biz-type="picking_receipt" :biz-no="currentRow?.orderNo"/>
</template>

<script setup lang="ts">
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { PickingReceiptApi, PickingReceiptVO } from '@/api/scm/inventory/pickingreceipt'
import { WorkOrderApi, WorkOrderVO } from '@/api/scm/mfg/workorder'
import { getRemoteUnit } from '@/utils/commonBiz'
import { formatAmount, formatQuantity } from '@/utils/formatter'

// 扩展PickingReceiptVO以包含明细数组
interface ExtendedPickingReceiptVO extends PickingReceiptVO {
  pickingReceiptDetails?: any[]
  details?: any[] // 添加 details 字段
}

// 扩展WorkOrderVO以包含明细数组
interface ExtendedWorkOrderVO extends WorkOrderVO {
  workOrderDetails?: any[]
}
import PickingReceiptForm from './PickingReceiptForm.vue'
import ApproveInfoForm from '../../base/approveinfo/ApproveInfoForm.vue'
import { DICT_TYPE,getDictLabel,getStrDictOptions } from '@/utils/dict'
import { useClipboard } from '@vueuse/core'
/** 领料出库 列表 */
defineOptions({ name: 'PickingReceipt' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter() // 路由跳转
const { copy } = useClipboard() // 复制功能

/** 复制订单单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

const subTabsName = ref('pendingPicking') // 默认显示待领料tab
const loading = ref(true) // 列表的加载中
const list = ref<ExtendedPickingReceiptVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const pendingTotal = ref(0) // 待领料总页数
const pickedTotal = ref(0) // 已领料总页数
const spanArr = ref<number[]>([]) // 行合并数组
const currentRow = ref() // 当前选中行
const isExpanded = ref(false) // 表单展开状态
const unitMap = ref<Map<number, string>>(new Map()) // 单位ID到名称的映射

// 工单相关数据
const workOrderLoading = ref(false) // 工单列表加载中
const workOrderList = ref<ExtendedWorkOrderVO[]>([]) // 工单列表数据
const workOrderTotal = ref(0) // 工单列表总页数
const selectedWorkOrders = ref<ExtendedWorkOrderVO[]>([]) // 选中的工单
const workOrderSpanArr = ref<number[]>([]) // 工单行合并数组
const workOrderUnitMap = ref<Map<number, string>>(new Map()) // 工单单位映射
const workOrderWarehouseMap = ref<Map<number, string>>(new Map()) // 工单仓库映射
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderNo: undefined,
  bizType: undefined,
  sourceType: undefined,
  sourceId: undefined,
  sourceNo: undefined,
  objectId: undefined,
  objectName: undefined,
  objectOrderNo: undefined,
  date: [],
  warehouseId: undefined,
  accountId: undefined,
  note: undefined,
  remark: undefined,
  createTime: [],
  approveStatus: undefined,
  approveNo: undefined,
  approverId: undefined,
  approverName: undefined,
  approveDate: [],
  deptId: undefined,
  empId: undefined,
  managerId: undefined,
  manger1Id: undefined,
  accountantId: undefined,
  checkerId: undefined,
  detail:true
})

// 工单查询参数
const workOrderQueryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  approvalStatus: '3', // 只查询已审核通过的工单
  pickingStatus: ['0', '2', '3'] // 未开始、已开始、部分完成的领料状态
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const selectedRows = ref<ExtendedPickingReceiptVO[]>([]) // 选中的行
const approveInfoFormRef = ref() // 审核弹窗引用
const pickingFormRef = ref() // 工单领料表单引用
const inventory_transaction_type = getStrDictOptions(DICT_TYPE.INVENTORY_TRANSACTION_TYPE)
const material_source = getStrDictOptions(DICT_TYPE.MATERIAL_SOURCE)
const approve_status = getStrDictOptions(DICT_TYPE.APPROVE_STATUS)
/** 判断订单是否已完全领料 */
const isOrderFullyPicked = (order: ExtendedPickingReceiptVO) => {
  if (!order.pickingReceiptDetails || order.pickingReceiptDetails.length === 0) {
    return false
  }

  return order.pickingReceiptDetails.every(detail => {
    const planned = parseFloat(detail.plannedQuantity) || 0
    const fulfilled = parseFloat(detail.fulfilledQuantity) || 0
    return fulfilled >= planned && planned > 0
  })
}

/** 判断订单是否有任何领料 */
const hasAnyPicking = (order: ExtendedPickingReceiptVO) => {
  if (!order.pickingReceiptDetails || order.pickingReceiptDetails.length === 0) {
    return false
  }

  return order.pickingReceiptDetails.some(detail => {
    const fulfilled = parseFloat(detail.fulfilledQuantity) || 0
    return fulfilled > 0
  })
}

/** 待领料数据 - 扁平化 */
const pendingPickingList = computed(() => {
  const result: any[] = []
  const pendingSpanArr: number[] = []

  list.value.forEach(order => {
    // 只显示未完全领料的订单
    if (!isOrderFullyPicked(order)) {
      const details = order.pickingReceiptDetails?.length ? order.pickingReceiptDetails : [{}]
      const detailCount = details.length

      details.forEach((detail: any, index: number) => {
        result.push({ ...order, detail })
        pendingSpanArr.push(index === 0 ? detailCount : 0)
      })
    }
  })

  // 更新行合并数组（仅在当前tab为待领料时）
  if (subTabsName.value === 'pendingPicking') {
    spanArr.value = pendingSpanArr
  }

  return result
})

/** 已领料数据 - 扁平化 */
const pickedList = computed(() => {
  const result: any[] = []
  const pickedSpanArr: number[] = []

  list.value.forEach(order => {
    // 显示所有订单，不进行过滤
    const details = order.pickingReceiptDetails?.length ? order.pickingReceiptDetails : [{}]
    const detailCount = details.length

    details.forEach((detail: any, index: number) => {
      result.push({ ...order, detail })
      pickedSpanArr.push(index === 0 ? detailCount : 0)
    })
  })

  // 更新行合并数组（仅在当前tab为已领料时）
  if (subTabsName.value === 'picked') {
    spanArr.value = pickedSpanArr
  }

  return result
})

/** 工单扁平化数据 - 将工单和明细数据合并 */
const flattenedWorkOrderList = computed(() => {
  const result: any[] = []
  workOrderSpanArr.value = [] // 每次重新计算时清空旧数据

  workOrderList.value.forEach(workOrder => {
    // 获取工单明细数据
    const details = workOrder.workOrderDetails?.length ? workOrder.workOrderDetails : [{}] // 确保无明细时也有占位行
    const detailCount = details.length

    details.forEach((detail: any, index: number) => {
      result.push({ ...workOrder, detail })
      // 主信息列只在第一个明细行合并，合并行数=明细数量
      workOrderSpanArr.value.push(index === 0 ? detailCount : 0)
    })
  })

  return result
})

/** 行合并方法 */
const objectSpanMethod = ({ row, column, rowIndex }: any) => {
  // 需要合并的主信息列 - 移除审批编号和审批人字段
  const mergeFields = ['orderNo', 'bizType', 'sourceType', 'sourceNo', 'objectName', 'date', 'approveStatus', 'objectOrderNo', 'note', 'remark', 'createTime']

  // 检查是否是操作列（通过列标签判断）
  const isOperationColumn = column.label === '操作'

  // 检查是否是选择列（通过列类型判断）
  const isSelectionColumn = column.type === 'selection'

  if (mergeFields.includes(column.property) || isOperationColumn || isSelectionColumn) {
    const span = spanArr.value[rowIndex]
    if (span > 0) {
      return {
        rowspan: span,
        colspan: 1
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }
}

/** 当前行变化 */
const handleCurrentChange = (row: any) => {
  currentRow.value = row
}

/** 选择变化处理 */
const handleSelectionChange = (selection: ExtendedPickingReceiptVO[]) => {
  selectedRows.value = selection
}

/** 切换表单展开状态 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

/** 加载单位数据 */
const loadUnits = async () => {
  try {
    // 批量获取所有单位信息
    const units = await getRemoteUnit()

    if (!units || units.length === 0) {
      return
    }

    // 建立单位映射
    units.forEach((unit: any) => {
      if (unit && unit.id && unit.name) {
        unitMap.value.set(unit.id, unit.name)
      }
    })
  } catch (error) {
    console.error('加载单位数据失败:', error)
  }
}

/** 获取单位名称 */
const getUnitName = (unitId: number | string) => {
  if (!unitId) return ''
  const id = typeof unitId === 'string' ? parseInt(unitId) : unitId
  const unitName = unitMap.value.get(id)
  return unitName || unitId.toString()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await PickingReceiptApi.getPickingReceiptPage(queryParams)
    list.value = data.list
    total.value = data.total

    // 直接使用列表中的 details 字段作为明细数据
    list.value.forEach(order => {
      // 将 details 字段赋值给 pickingReceiptDetails，保持原有的属性名
      order.pickingReceiptDetails = order.details || []
    })

    // 更新各tab的分页总数
    updateTabTotals()
  } finally {
    loading.value = false
  }
}

/** 更新各tab的分页总数 */
const updateTabTotals = () => {
  let pendingCount = 0
  let pickedCount = 0

  list.value.forEach(order => {
    if (!isOrderFullyPicked(order)) {
      pendingCount += order.pickingReceiptDetails?.length || 1
    }
    if (hasAnyPicking(order)) {
      pickedCount += order.pickingReceiptDetails?.length || 1
    }
  })

  pendingTotal.value = pendingCount
  pickedTotal.value = pickedCount
}

/** 查询工单列表 */
const getWorkOrderList = async () => {
  workOrderLoading.value = true
  try {
    const data = await WorkOrderApi.getWorkOrderPage(workOrderQueryParams)
    workOrderList.value = data.list
    workOrderTotal.value = data.total

    // 加载工单明细数据
    await loadWorkOrderDetails()
  } finally {
    workOrderLoading.value = false
  }
}

/** 加载工单明细数据 */
const loadWorkOrderDetails = async () => {
  try {
    // 为每个工单加载明细数据
    const detailPromises = workOrderList.value.map(async (workOrder) => {
      try {
        const details = await WorkOrderApi.getWorkOrderDetailListByBizOrderId(workOrder.id)
        workOrder.workOrderDetails = Array.isArray(details) ? details : []
      } catch (error) {
        console.error(`加载工单${workOrder.id}明细失败:`, error)
        workOrder.workOrderDetails = []
      }
    })

    await Promise.all(detailPromises)
  } catch (error) {
    console.error('加载工单明细数据失败:', error)
  }
}

/** 工单选择变化处理 */
const handleWorkOrderSelectionChange = (selection: any[]) => {
  selectedWorkOrders.value = selection
}

/** 工单行合并方法 */
const workOrderSpanMethod = ({ row, column, rowIndex, columnIndex }: any) => {
  // 主表字段列表（需要合并的字段）
  const mergeFields = [
    'workNo', 'productCode', 'productName', 'spec', 'orderQuantity', 'scheduleQuantity', 'schedulePiece',
    'scheduleStartTime', 'scheduleEndTime', 'orderType', 'orderNo', 'bomCode', 'status', 'approvalStatus',
    'pickingStatus', 'inStockStatus', 'reportStatus', 'qualityStatus', 'orderDate', 'deliverDate',
    'scheduleCostTime', 'scheduleLine', 'scheduleHeadcount', 'requirement', 'remark', 'progress',
    'actualLine', 'actualQuantity', 'actualStartTime', 'actualEndTime', 'actualCostTime', 'actualHeadcount',
    'actualPiece', 'actualBatchNo', 'actualRemark', 'createTime', 'approveNo', 'approvalTime'
  ]

  // 操作列也需要合并（通过列索引判断，因为操作列没有property）
  const isOperationColumn = columnIndex === flattenedWorkOrderList.value.length > 0 ?
    Object.keys(flattenedWorkOrderList.value[0]).length : false

  if (mergeFields.includes(column.property) || column.label === '操作') {
    return {
      rowspan: workOrderSpanArr.value[rowIndex],
      colspan: workOrderSpanArr.value[rowIndex] > 0 ? 1 : 0
    }
  }
  return { rowspan: 1, colspan: 1 }
}

/** 工单表格汇总方法 */
const workOrderSummaryMethod = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []

  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 主表数量字段（需要去重汇总）
    const mainQuantityFields = [
      'orderQuantity', 'scheduleQuantity', 'schedulePiece', 'actualQuantity', 'actualPiece'
    ]

    // 明细数量字段（直接汇总）
    const detailQuantityFields = [
      'detail.plannedQuantity', 'detail.fulfilledQuantity', 'detail.standardPlannedQuantity', 'detail.standardFulfilledQuantity'
    ]

    if (mainQuantityFields.includes(column.property)) {
      // 主表字段需要去重汇总（只计算每个工单的第一行）
      const uniqueWorkOrders = new Map()
      data.forEach(item => {
        if (!uniqueWorkOrders.has(item.id)) {
          const value = Number(item[column.property]) || 0
          uniqueWorkOrders.set(item.id, value)
        }
      })
      const total = Array.from(uniqueWorkOrders.values()).reduce((prev, curr) => prev + curr, 0)
      if (total > 0) {
        sums[index] = formatQuantity(total)
      } else {
        sums[index] = ''
      }
    } else if (detailQuantityFields.includes(column.property)) {
      // 明细字段直接汇总
      const values = data.map(item => {
        const value = item.detail?.[column.property.split('.')[1]]
        return Number(value) || 0
      })
      const total = values.reduce((prev, curr) => prev + curr, 0)
      if (total > 0) {
        sums[index] = formatQuantity(total)
      } else {
        sums[index] = ''
      }
    } else {
      sums[index] = ''
    }
  })

  return sums
}

/** 加载工单基础数据 */
const loadWorkOrderBasicData = async () => {
  try {
    // 加载单位数据
    const units = await getRemoteUnit()
    if (units && units.length > 0) {
      units.forEach((unit: any) => {
        if (unit && unit.id && unit.name) {
          workOrderUnitMap.value.set(unit.id, unit.name)
        }
      })
    }

    // 加载仓库数据
    const { WarehouseApi } = await import('@/api/scm/inventory/warehouse')
    const warehouseList = await WarehouseApi.getWarehouseList({ pageNo: 1, pageSize: 100 })
    if (Array.isArray(warehouseList)) {
      warehouseList.forEach((warehouse: any) => {
        if (warehouse && warehouse.id && warehouse.name) {
          workOrderWarehouseMap.value.set(warehouse.id, warehouse.name)
        }
      })
    }
  } catch (error) {
    console.error('加载工单基础数据失败:', error)
  }
}

/** 根据单位ID获取单位名称（工单用） */
const getWorkOrderUnitName = (unitId: number | string): string => {
  if (!unitId) return ''

  if (typeof unitId === 'string' && isNaN(Number(unitId))) {
    return unitId
  }

  const id = typeof unitId === 'string' ? parseInt(unitId) : unitId
  return workOrderUnitMap.value.get(id) || unitId.toString()
}

/** 根据仓库ID获取仓库名称（工单用） */
const getWorkOrderWarehouseName = (warehouseId: number | string): string => {
  if (!warehouseId) return ''

  if (typeof warehouseId === 'string' && isNaN(Number(warehouseId))) {
    return warehouseId
  }

  const id = typeof warehouseId === 'string' ? parseInt(warehouseId) : warehouseId
  return workOrderWarehouseMap.value.get(id) || `仓库${warehouseId}`
}

/** 打开领料表单 - 添加防抖机制 */
const isPickingFormOpening = ref(false)

const openPickingForm = async (type: string, workOrder: WorkOrderVO) => {
  // 防止重复点击
  if (isPickingFormOpening.value) {
    return
  }

  try {
    isPickingFormOpening.value = true
    await pickingFormRef.value.open(type, null, workOrder)
  } finally {
    // 延迟重置状态，避免快速连续点击
    setTimeout(() => {
      isPickingFormOpening.value = false
    }, 500)
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 查看详情 - 跳转到详情页面 */
const handleViewDetail = (id: number) => {
  push(`/scm/inventory/pickingreceipt/detail/${id}`)
}



/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PickingReceiptApi.deletePickingReceipt(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PickingReceiptApi.exportPickingReceipt(queryParams)
    download.excel(data, '领料出库.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = [
      'detail.fulfilledQuantity', 'detail.plannedQuantity',
      'detail.standardFulfilledQuantity', 'detail.standardPlannedQuantity',
      'detail.invoiceQuantity', 'detail.standardInvoiceQuantity'
    ]

    // 需要汇总的金额字段
    const amountFields = [
      'detail.unitPrice', 'detail.amount', 'detail.taxPrice',
      'detail.taxAmount', 'detail.invoiceAmount'
    ]

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => {
        const value = column.property.split('.').reduce((obj, key) => obj?.[key], item)
        return Number(value) || 0
      })
      const total = values.reduce((prev, curr) => prev + curr, 0)
      if (total > 0) {
        sums[index] = formatQuantity(total)
      } else {
        sums[index] = ''
      }
    } else if (amountFields.includes(column.property)) {
      // 金额字段汇总
      const values = data.map(item => {
        const value = column.property.split('.').reduce((obj, key) => obj?.[key], item)
        return Number(value) || 0
      })
      const total = values.reduce((prev, curr) => prev + curr, 0)
      if (total > 0) {
        sums[index] = formatAmount(total)
      } else {
        sums[index] = ''
      }
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

/** 审核按钮操作 */
const handleApproval = async () => {
  console.log('handleApproval', selectedRows.value)
  if(!selectedRows.value || selectedRows.value.length === 0) {
    message.error('请选择要审核的领料单！')
    return
  }

  // 如果选中了多个领料单，提示用户
  if(selectedRows.value.length > 1) {
    message.warning('当前只支持单个领料单审核，请选择一个领料单进行审核')
    return
  }

  // 设置当前行为选中的第一个领料单
  const selectedOrder = selectedRows.value[0]
  currentRow.value = selectedOrder

  // 发起审批，传入业务数据
  approveInfoFormRef.value.open('approve', undefined, {
    bizId: selectedOrder.id,
    bizNo: selectedOrder.orderNo,
    bizType: 'picking_receipt'
  })
}

/** 监听tab切换 */
watch(subTabsName, async (newVal) => {
  if (newVal === 'pendingPicking') {
    // 切换到待领料tab时，加载工单数据
    await loadWorkOrderBasicData()
    await getWorkOrderList()
    nextTick(() => {
      // 触发待领料列表的计算属性重新计算
      pendingPickingList.value
    })
  } else if (newVal === 'picked') {
    nextTick(() => {
      // 触发已领料列表的计算属性重新计算
      pickedList.value
    })
  }
})

/** 初始化 **/
onMounted(async () => {
  // 先加载单位数据
  await loadUnits()
  // 再加载列表数据
  getList()
  // 如果默认显示待领料tab，也加载工单数据
  if (subTabsName.value === 'pendingPicking') {
    await loadWorkOrderBasicData()
    getWorkOrderList()
  }
})
</script>

<style scoped>
/* 单号链接样式 */
.order-link {
  font-weight: 500;
  color: #409eff;
}

.order-link:hover {
  color: #66b1ff;
}

/* 单号容器样式 */
.order-no-container, .work-no-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  padding: 4px 0;
}

/* 订单号行布局 */
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}

/* 复制按钮样式 */
.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}



.copy-btn:hover {
  opacity: 1;
}

/* 生产单号文本样式 */
.work-no-text {
  font-weight: 500;
  color: #303133;
}

/* 状态标签样式 */
.status-tag :deep(.el-tag) {
  font-size: 9px !important;
  padding: 1px 4px !important;
  height: 16px !important;
  line-height: 14px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  flex-shrink: 0;
}
</style>
