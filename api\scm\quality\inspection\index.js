import request from "../../../../utils/request";

// 查询质检单分页
export function getInspectionPageApi(params) {
	return request({
		url: '/scm/quality/inspection/page',
		method: 'GET',
		params
	})
}

// 查询质检单详情
export function getInspectionApi(id) {
	return request({
		url: '/scm/quality/inspection/get?id=' + id,
		method: 'GET'
	})
}

// 根据工单ID查询质检单列表
export function getInspectionListByWorkOrderIdApi(workOrderId) {
	return request({
		url: '/scm/quality/inspection/list-by-work-order-id?workOrderId=' + workOrderId,
		method: 'GET'
	})
}

// 获得质检明细列表
export function getInspectionItemListByInspectionIdApi(inspectionId) {
	return request({
		url: '/scm/quality/inspection/inspection-item/list-by-inspection-id?inspectionId=' + inspectionId,
		method: 'GET'
	})
}
