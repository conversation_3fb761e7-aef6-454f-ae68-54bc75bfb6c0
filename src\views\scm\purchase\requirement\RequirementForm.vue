<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="65%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="需求编号" prop="requestNo">
            <el-input v-model="formData.requestNo" placeholder="保存时自动生成" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料" prop="materialId">
            <ScrollSelect
              v-model="formData.materialId"
              :load-method="loadMaterials"
              :label-key="formatMaterialLabel"
              value-key="id"
              query-key="name"
              :default-value="materialDefaultValue"
              @change="handleMaterialChange"
              placeholder="请选择物料"
              style="width: 100%"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="需求日期" prop="requirementDate">
            <el-date-picker
              v-model="formData.requirementDate"
              type="date"
              value-format="x"
              placeholder="选择需求日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="物料编号" prop="materialCode">
            <el-input v-model="formData.materialCode" placeholder="请输入物料编号" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料规格" prop="materialSpec">
            <el-input v-model="formData.materialSpec" placeholder="请输入物料规格" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料类型" prop="materialType">
            <el-select v-model="formData.materialType" placeholder="请选择物料类型" style="width: 100%" disabled>
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.MATERIAL_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="需求部门" prop="departmentId">
            <el-tree-select
              v-model="formData.departmentId"
              :data="deptTree"
              :props="deptTreeProps"
              placeholder="请选择需求部门"
              style="width: 100%"
              clearable
              filterable
              check-strictly
              :key="`dept-tree-${deptTree.length}`"
              node-key="id"
              :default-expanded-keys="formData.departmentId ? [formData.departmentId] : []"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="需求数量" prop="quantity">
            <el-input v-model="formData.quantity" placeholder="请输入物料数量">
              <template #append>
                <el-select
                  v-model="formData.unit"
                  placeholder="请选择单位"
                  style="width: 100px"
                  @change="handleUnitSelectChange"
                >
                  <el-option
                    v-for="dict in unitList"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id.toString()"
                  />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="期望交货日期" prop="expectedDeliveryDate">
            <el-date-picker
              v-model="formData.expectedDeliveryDate"
              type="date"
              value-format="x"
              placeholder="选择期望交货日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%" disabled>
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.PURCHASE_REQ_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="需求来源类型" prop="sourceType">
            <el-select
              v-model="formData.sourceType"
              placeholder="请选择需求来源类型"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.PURCHASE_REQ_SOURCE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="来源单号" prop="sourceNo">
            <el-input v-model="formData.sourceNo" placeholder="请输入来源单号" disabled/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <!-- <el-col :span="8">
          <el-form-item label="业务订单ID" prop="bizOrderId">
            <el-input v-model="formData.bizOrderId" placeholder="请输入业务订单ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="订单类型" prop="bizOrderType">
            <el-select
              v-model="formData.bizOrderType"
              placeholder="请选择需求来源类型"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.SCM_BIZ_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="订单编号" prop="bizOrderNo">
            <el-input v-model="formData.bizOrderNo" placeholder="请输入业务订单编号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购订单" prop="purchaseNo">
            <el-input v-model="formData.purchaseNo" placeholder="请输入采购订单" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="摘要" prop="note">
            <el-input v-model="formData.note" placeholder="请输入摘要" />
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { nextTick, watch } from 'vue'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { RequirementApi, RequirementVO } from '@/api/scm/purchase/requirement'
import { MaterialApi } from '@/api/scm/base/material'
import { UnitApi } from '@/api/scm/base/unit'
import * as DeptApi from '@/api/system/dept'
import { handleTree } from '@/utils/tree'
import ScrollSelect from '@/components/ScrollSelect/index.vue'

/** 采购需求 表单 */
defineOptions({ name: 'RequirementForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined as number | undefined,
  requestNo: undefined as string | undefined,
  materialId: undefined as number | undefined,
  materialName: undefined as string | undefined,
  requirementDate: undefined as string | undefined,
  materialCode: undefined as string | undefined,
  materialSpec: undefined as string | undefined,
  materialType: undefined as string | undefined,
  departmentId: undefined as number | undefined,
  quantity: undefined as number | undefined,
  unitPrice: undefined as number | undefined,
  unit: undefined as string | undefined,
  amount: undefined as number | undefined,
  expectedDeliveryDate: undefined as string | undefined,
  status: undefined as string | undefined,
  totalAmount: undefined as number | undefined,
  sourceType: undefined as string | undefined,
  sourceId: undefined as number | undefined,
  sourceNo: undefined as string | undefined,
  sourceDetailId: undefined as number | undefined,
  bizOrderId: undefined as number | undefined,
  bizOrderNo: undefined as string | undefined,
  bizOrderType: undefined as string | undefined,
  remark: undefined as string | undefined,
  note: undefined as string | undefined,
  purchaseNo: undefined as string | undefined,
  detailId: undefined as number | undefined
})
const formRules = reactive({
  materialId: [{ required: true, message: '物料不能为空', trigger: 'blur' }],
  requirementDate: [{ required: true, message: '需求日期不能为空', trigger: 'blur' }],
  quantity: [{ required: true, message: '物料数量不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

// 物料数据
const materialDefaultValue = ref({})

// 单位数据
const unitDefaultValue = ref({})

// 部门数据
const deptTree = ref<any[]>([])
const deptTreeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

// 监听部门树数据变化，确保组件正确显示
watch(deptTree, (newDeptTree) => {
  if (newDeptTree.length > 0 && formData.value.departmentId) {
    // 强制触发组件重新渲染
    nextTick(() => {
      // 确保组件正确显示
    })
  }
}, { deep: true })

// 获取部门树数据
const getDeptTree = async () => {
  try {
    const data = await DeptApi.getSimpleDeptList()
    deptTree.value = handleTree(data)
  } catch (error) {
    deptTree.value = []
  }
}

// 格式化物料选择器显示标签
const formatMaterialLabel = (material: any) => {
  if (!material) return ''
  const fullCode = material.fullCode || ''
  const name = material.name || ''
  const spec = material.spec || ''

  // 只包含有值的字段
  const parts = [fullCode, name, spec].filter(part => part && part.trim())
  return parts.join(' - ')
}

// ScrollSelect 加载物料数据的方法
const loadMaterials = async (params: any) => {
  try {
    const data = await MaterialApi.getSimpleMaterialPage({
      pageNo: params.pageNo || 1,
      pageSize: params.pageSize || 20,
      name: params.name || undefined
    })
    return {
      list: data.list || [],
      total: data.total || 0
    }
  } catch (error) {
    console.error('获取物料列表失败:', error)
    return {
      list: [],
      total: 0
    }
  }
}

const unitList = ref<any[]>([])
const getUnitList = async () => {
  try {
    const data = await UnitApi.getUnitPage({
      pageNo: 1,
      pageSize: 100
    })
    unitList.value = data.list || []
  } catch (error) {
    unitList.value = []
  }
}
// ScrollSelect 加载单位数据的方法
const loadUnits = async (params: any) => {
  try {
    const data = await UnitApi.getUnitPage({
      pageNo: params.pageNo || 1,
      pageSize: params.pageSize || 20,
      name: params.name || undefined
    })
    return {
      list: data.list || [],
      total: data.total || 0
    }
  } catch (error) {
    return {
      list: [],
      total: 0
    }
  }
}

// 处理单位选择变化
const handleUnitChange = (unitId: number, selectedUnit: any) => {
  if (unitId && selectedUnit) {
    // 设置单位默认值，用于显示
    unitDefaultValue.value = {
      id: selectedUnit.id,
      name: selectedUnit.name
    }
  } else {
    unitDefaultValue.value = {}
  }
}

// 处理单位下拉选择变化
const handleUnitSelectChange = (unitIdString: string) => {
  if (unitIdString) {
    // 从unitList中找到对应的单位信息
    const selectedUnit = unitList.value.find(unit => unit.id.toString() === unitIdString)
    if (selectedUnit) {
      unitDefaultValue.value = {
        id: selectedUnit.id,
        name: selectedUnit.name
      }
    }
  } else {
    unitDefaultValue.value = {}
  }
}

// 处理物料选择变化
const handleMaterialChange = async (materialId: number, selectedMaterial: any) => {
  if (materialId && selectedMaterial) {
    // 自动填充物料相关信息
    formData.value.materialName = selectedMaterial.name
    formData.value.materialCode = selectedMaterial.fullCode
    formData.value.materialSpec = selectedMaterial.spec
    formData.value.materialType = selectedMaterial.type
    formData.value.unitPrice = selectedMaterial.purchasePrice

    // 设置采购单位
    if (selectedMaterial.purchaseUnit) {
      // 获取单位详细信息以设置默认值
      try {
        const unitInfo = await UnitApi.getUnit(selectedMaterial.purchaseUnit)
        if (unitInfo) {
          // 确保formData.unit是string类型，与API定义一致
          formData.value.unit = unitInfo.id.toString()
          unitDefaultValue.value = {
            id: unitInfo.id,
            name: unitInfo.name
          }
        }
      } catch (error) {
        // 如果获取单位信息失败，直接设置原值并转换为string
        formData.value.unit = selectedMaterial.purchaseUnit.toString()
      }
    }
  } else {
    // 清空物料相关信息
    formData.value.materialName = undefined
    formData.value.materialCode = undefined
    formData.value.materialSpec = undefined
    formData.value.materialType = undefined
    formData.value.unit = undefined
    formData.value.unitPrice = undefined
    unitDefaultValue.value = {}
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number, prefillData?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 确保单位列表数据已加载完成
  if (unitList.value.length === 0) {
    await getUnitList()
  }

  // 加载部门数据
  await getDeptTree()

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await RequirementApi.getRequirement(id)

      // 处理部门ID类型转换 - 确保与部门树数据类型一致
      if (data.departmentId && typeof data.departmentId === 'string') {
        data.departmentId = parseInt(data.departmentId, 10)
      }

      formData.value = data

      // 设置物料默认值，用于ScrollSelect显示
      if (data.materialId && data.materialName) {
        materialDefaultValue.value = {
          id: data.materialId,
          name: data.materialName
        }
      }

      // 等待DOM更新，确保el-tree-select能正确显示部门名称
      if (data.departmentId) {
        await nextTick()
      }

      // 检查物料类型数据是否存在，如果不存在则从后端获取
      if (data.materialId && (!data.materialType || data.materialType === undefined || data.materialType === null)) {
        try {
          const materialInfo = await MaterialApi.getMaterial(data.materialId)

          // 确保materialInfo存在且有效
          if (materialInfo) {
            // 更新物料类型数据
            if (materialInfo.type) {
              formData.value.materialType = materialInfo.type
            }

            // 如果其他物料相关字段也缺失，一并更新
            if (!data.materialCode && materialInfo.fullCode) {
              formData.value.materialCode = materialInfo.fullCode
            }
            if (!data.materialSpec && materialInfo.spec) {
              formData.value.materialSpec = materialInfo.spec
            }
            if (!data.unitPrice && materialInfo.purchasePrice) {
              formData.value.unitPrice = materialInfo.purchasePrice
            }
          }
        } catch (error) {
          console.error('获取物料详细信息失败:', error)
        }
      }

      // 处理单位数据 - 确保单位ID能正确匹配到unitList中的选项
      if (data.unit) {
        // 设置单位默认值，用于ScrollSelect显示
        try {
          const unitInfo = await UnitApi.getUnit(data.unit)
          if (unitInfo) {
            unitDefaultValue.value = {
              id: unitInfo.id,
              name: unitInfo.name
            }
            // 确保formData中的单位值与unitInfo的ID一致（保持string类型以匹配API定义）
            formData.value.unit = unitInfo.id.toString()
          }
        } catch (error) {
          console.error('获取单位信息失败:', error)
        }
      }
    } catch (error) {
      console.error('设置表单数据失败:', error)
    } finally {
      formLoading.value = false
    }
  }

  // 如果有预填充数据，则设置到表单中
  if (prefillData) {
    // 处理部门ID类型转换
    if (prefillData.departmentId && typeof prefillData.departmentId === 'string') {
      prefillData.departmentId = parseInt(prefillData.departmentId, 10)
    }

    // 合并预填充数据到表单数据中
    Object.assign(formData.value, prefillData)

    // 设置物料默认值
    if (prefillData.materialId && prefillData.materialName) {
      materialDefaultValue.value = {
        id: prefillData.materialId,
        name: prefillData.materialName
      }

      // 检查物料类型数据是否存在，如果不存在则从后端获取
      if (!prefillData.materialType || prefillData.materialType === undefined || prefillData.materialType === null) {
        try {
          const materialInfo = await MaterialApi.getMaterial(prefillData.materialId)

          // 确保materialInfo存在且有效
          if (materialInfo) {
            // 更新物料类型数据
            if (materialInfo.type) {
              formData.value.materialType = materialInfo.type
            }

            // 如果其他物料相关字段也缺失，一并更新
            if (!prefillData.materialCode && materialInfo.fullCode) {
              formData.value.materialCode = materialInfo.fullCode
            }
            if (!prefillData.materialSpec && materialInfo.spec) {
              formData.value.materialSpec = materialInfo.spec
            }
            if (!prefillData.unitPrice && materialInfo.purchasePrice) {
              formData.value.unitPrice = materialInfo.purchasePrice
            }
          }
        } catch (error) {
          console.error('获取物料详细信息失败:', error)
        }
      }
    }

    // 处理单位数据 - 确保单位ID格式正确
    if (prefillData.unit) {
      // 确保formData.unit是string类型，与API定义一致
      if (typeof prefillData.unit === 'number') {
        formData.value.unit = prefillData.unit.toString()
      }
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as RequirementVO
    if (formType.value === 'create' || formType.value === 'transferPurchase') {
      await RequirementApi.createRequirement(data)
      message.success(t('common.createSuccess'))
    } else {
      await RequirementApi.updateRequirement(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    requestNo: undefined,
    materialId: undefined,
    materialName: undefined,
    requirementDate: undefined,
    materialCode: undefined,
    materialSpec: undefined,
    materialType: undefined,
    departmentId: undefined,
    quantity: undefined,
    unitPrice: undefined,
    unit: undefined,
    amount: undefined,
    expectedDeliveryDate: undefined,
    status: undefined,
    totalAmount: undefined,
    sourceType: undefined,
    sourceNo: undefined,
    bizOrderId: undefined,
    bizOrderNo: undefined,
    bizOrderType: undefined,
    remark: undefined,
    note: undefined,
    purchaseNo: undefined,
    detailId: undefined ,
  }
  formRef.value?.resetFields()

  // 重置默认值
  materialDefaultValue.value = {}
  unitDefaultValue.value = {}
}

// 移除组件挂载时的单位数据初始化，改为在打开弹窗时按需加载
</script>
