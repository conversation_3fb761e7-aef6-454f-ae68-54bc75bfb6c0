<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="80%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading" inline
    >
      <el-form-item label="订单编号" prop="orderNo">
        <el-input v-model="formData.orderNo" placeholder="保存时自动生成" disabled  class="!w-240px"/>
      </el-form-item>
      <el-form-item label="报价单" prop="quoteId">
        <ScrollSelect
              filterable
              clearable
              class="!w-240px"
              v-model="formData.quoteId"
              placeholder="请选择报价单"
              :initial-load="false"
              :load-method="getRemoteQuoteApply"
              :label-key="(item) => `${item.productName} || ${item.customerName} `"
              value-key="id"
              :default-value="{ 
                value: formData.quoteId, 
                label: formData.quoteId
              }"
              :query-key="productName"
              @change="async (val, quote) => {
                // formData.customerId = quote.customerId
                console.log('quote', quote, val)
                formData.customerName = quote.customerName
              }"
            />
      </el-form-item>
      <el-form-item label="客户" prop="customerId" >
        <ScrollSelect
          filterable
          clearable
          class="!w-240px"
          v-model="formData.customerId"
          placeholder="请选择客户"
          :initial-load="false"
          :load-method="getRemoteCustomer"
          :default-value="customerDefaultValue"
          :query-key="name"
          value-key="id"
          label-key="name"
          :extra-params="{
                isCustomer: 1,
              }"
          @change="(val, customer) => {
            console.log('customer:', val, customer)
            formData.customerName = customer?.name
          }"
        />
<!--        <el-select-v2
          v-model="formData.customerId"
          placeholder="请选择客户"
          class="!w-240px"
          :remote-method="getRemoteCustomer"
          remote
          :options="customerList"
          filterable
          clearable
          @change="(val) => {
            const customer = customerList.find((item) => item.value === val)
            formData.customerName = customer?.label
          }"
        >
          <template #label="{ label }">
            <span>{{ label }}</span>
          </template>
        </el-select-v2>-->
      </el-form-item>

      <el-form-item label="订单日期" prop="orderDate">
        <el-date-picker
          v-model="formData.orderDate"
          type="date"
          value-format="x"
          placeholder="选择订单创建日期"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="配送地址" prop="shippingAddress">
        <el-input v-model="formData.shippingAddress" placeholder="请输入配送地址" class="!w-240px"/>
      </el-form-item>
      <el-form-item label="账单地址" prop="billingAddress">
        <el-input v-model="formData.billingAddress" placeholder="请输入账单地址" class="!w-240px"/>
      </el-form-item>
      <el-form-item label="订单备注" prop="remarks">
        <el-input v-model="formData.remarks" placeholder="请输入订单备注" class="!w-240px"/>
      </el-form-item>
      <!-- <el-form-item label="来源单号" prop="orderSource">
        <el-input v-model="formData.orderSource" placeholder="请输入来源单号" class="!w-240px"/>
      </el-form-item> -->
    
      <el-form-item label="预计送达日期" prop="estimatedDeliveryDate">
        <el-date-picker
          v-model="formData.estimatedDeliveryDate"
          type="date"
          value-format="x"
          placeholder="选择预计送达日期"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="配送方式" prop="deliveryMethod">
        <el-select v-model="formData.deliveryMethod" placeholder="请选择配送方式" class="!w-240px">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.TRADE_DELIVERY_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="支付方式" prop="paymentMethod">
        <el-select v-model="formData.paymentMethod" placeholder="请选择支付方式" class="!w-240px">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.PAYMENT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务员" prop="salemanId">
        <el-select
          v-model="formData.salemanId"
          placeholder="请选择业务员"
          class="!w-240px"
          filterable
          clearable
          @change="(val) => {
            const saleman = userList.find((item) => item.id === val)
            formData.salemanName = saleman?.nickname
            formData.salemanDeptId = saleman?.deptId
            formData.salemanDeptName = saleman?.deptName
          }"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务部门" prop="salemanDeptName">
        <el-input v-model="formData.salemanDeptName" placeholder="请输入业务部门名称" class="!w-240px" />
      </el-form-item>
    </el-form>
     <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="订单产品" name="orderDetail">
        <OrderDetailForm ref="orderDetailFormRef" :order-id="formData.id" />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { OrderApi, OrderVO } from '@/api/scm/sale/order'
import OrderDetailForm from './components/OrderDetailForm.vue'
import { CompanyApi, CompanyVO } from '@/api/scm/base/company'
import { debounce } from 'min-dash'
import {getSimpleUserList, UserVO} from '@/api/system/user'
import { getRemoteQuoteApply } from '@/utils/commonBiz'
import { ApplyApi } from '@/api/scm/quote/apply'
import { FormulaApi } from '@/api/scm/rd/formula'

/** 销售订单 表单 */
defineOptions({ name: 'OrderForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  orderId: undefined,
  orderNo: undefined,
  customerName: undefined,
  customerId: undefined,
  orderDate: new Date().getTime(),
  approvalNo: undefined,
  approvalStatus: undefined,
  approverId: undefined,
  approverName: undefined,
  orderStatus: undefined,
  totalAmount: undefined,
  paymentStatus: undefined,
  shippingAddress: undefined,
  billingAddress: undefined,
  remarks: undefined,
  orderSource: undefined,
  trackingNumber: undefined,
  estimatedDeliveryDate: undefined,
  deliveryMethod: undefined,
  taxAmount: undefined,
  taxType: undefined,
  discountAmount: undefined,
  discountType: undefined,
  paymentMethod: undefined,
  invoiceStatus: undefined,
  mfgStatus: undefined,
  closeStatus: undefined,
  salemanId: undefined,
  salemanName: undefined,
  salemanDeptId: undefined,
  salemanDeptName: undefined,
  kdId: undefined,
})
const formRules = reactive({
  customerId: [{ required: true, message: '客户不能为空', trigger: 'blur' }],
  customerName: [{ required: true, message: '客户姓名不能为空', trigger: 'blur' }],
  orderDate: [{ required: true, message: '订单创建日期和时间不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
/** 子表的表单 */
const subTabsName = ref('orderDetail')
const orderDetailFormRef = ref()
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  console.log(type,id)
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await OrderApi.getOrder(id)
      initSelect()
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await orderDetailFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'orderDetail'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OrderVO
    // 拼接子表的数据
    data.orderDetails = orderDetailFormRef.value.getData()
    if (formType.value === 'create') {
      await OrderApi.createOrder(data)
      message.success(t('common.createSuccess'))
    } else {
      await OrderApi.updateOrder(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 修改customerList声明部分
const customerList = ref<Array<{
  value: number | undefined
  label: string | undefined
  code?: string | undefined
}>>([])

const getRemoteCustomer = async (params) => {
  const { pageNo, pageSize, query, ...restParams } = params;
  const response = await CompanyApi.getSimpleCompanyPage({
    pageNo,
    pageSize,
    name: query, // 映射到 API 的 `name` 字段
    ...restParams,
  });
  const { list, total } = await response;
  return { list, total };
}

const initSelect = async () => {
  // 初始化下拉框
  customerList.value = [
        {
          value: formData.value.customerId,
          label: formData.value.customerName,
        },
      ]
}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    orderId: undefined,
    orderNo: undefined,
    customerName: undefined,
    customerId: undefined,
    orderDate: new Date().getTime(),
    approvalNo: undefined,
    approvalStatus: undefined,
    approverId: undefined,
    approverName: undefined,
    orderStatus: undefined,
    totalAmount: undefined,
    paymentStatus: undefined,
    shippingAddress: undefined,
    billingAddress: undefined,
    remarks: undefined,
    orderSource: undefined,
    trackingNumber: undefined,
    estimatedDeliveryDate: undefined,
    deliveryMethod: undefined,
    taxAmount: undefined,
    taxType: undefined,
    discountAmount: undefined,
    discountType: undefined,
    paymentMethod: undefined,
    invoiceStatus: undefined,
    mfgStatus: undefined,
    closeStatus: undefined,
    salemanId: undefined,
    salemanName: undefined,
    salemanDeptId: undefined,
    salemanDeptName: undefined,
    kdId: undefined,
  }
  formRef.value?.resetFields()
}

const userList = ref<UserVO[]>([]) // 用户列表
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}

// 计算客户选择器的默认值
const customerDefaultValue = computed(() => {
  if (formData.value.customerId) {
    return {
      id: formData.value.customerId,
      name: formData.value.customerName
    }
  }
  return undefined
})

// 获取用户列表
getUserList()
</script>
