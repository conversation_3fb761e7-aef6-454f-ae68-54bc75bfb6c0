# 领料弹窗请求优化说明

## 问题分析

在打开领料弹窗时，会同时发送多次重复的请求，主要原因包括：

### 1. 主表单重复请求
- `PickingReceiptForm.vue` 的 `open` 方法每次都会调用 `initWorkOrderList()` 获取工作订单列表
- 即使从工作订单页面点击"领料"按钮时，已经传入了工单数据，仍然会重复请求工单列表

### 2. 子表单组件重复初始化
- `PickingReceiptDetailForm.vue` 在 `onMounted` 时会并发调用多个初始化方法：
  - `initLocationList()` - 获取库位数据
  - `loadUnits()` - 获取单位数据  
  - `initWarehouseList()` - 获取仓库数据
- 这些请求每次打开弹窗都会重新执行

### 3. 用户快速点击导致重复请求
- 用户快速连续点击"领料"按钮时，会触发多次弹窗打开，导致重复请求

## 优化方案

### 1. 主表单优化 (`PickingReceiptForm.vue`)

```javascript
/** 打开弹窗 */
const open = async (type: string, id?: number, workOrderData?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 优化：只在需要时才加载工作订单列表，避免重复请求
  if (!workOrderData && type === 'create') {
    await initWorkOrderList()
  }
  
  // ... 其他逻辑
}
```

**优化点：**
- 只有在没有传入工单数据且为创建模式时，才加载工作订单列表
- 避免了从工作订单页面点击领料时的重复请求

### 2. 工作订单列表缓存优化

```javascript
//初始化工作订单数据 - 添加缓存机制
const initWorkOrderList = async () => {
  // 如果已经加载过工作订单列表，直接返回
  if (workOrderList.value.length > 0) {
    return
  }
  
  // ... 请求逻辑
}
```

**优化点：**
- 添加缓存机制，避免重复请求相同数据
- 在同一个会话中，工作订单列表只加载一次

### 3. 子表单组件优化 (`PickingReceiptDetailForm.vue`)

#### 3.1 添加缓存标志
```javascript
// 添加缓存标志，避免重复请求
const isLocationListLoaded = ref(false)
const isUnitsLoaded = ref(false)
const isWarehouseListLoaded = ref(false)
```

#### 3.2 优化初始化方法
```javascript
//初始化方法 - 优化为按需加载
onMounted(async () => {
  // 并行加载基础数据，但添加缓存机制避免重复请求
  const initPromises = []
  
  if (!isLocationListLoaded.value) {
    initPromises.push(initLocationList().then(() => { isLocationListLoaded.value = true }))
  }
  
  if (!isUnitsLoaded.value) {
    initPromises.push(loadUnits().then(() => { isUnitsLoaded.value = true }))
  }
  
  if (!isWarehouseListLoaded.value) {
    initPromises.push(initWarehouseList().then(() => { isWarehouseListLoaded.value = true }))
  }
  
  // 等待所有基础数据加载完成
  await Promise.all(initPromises)
  
  // ... 其他逻辑
})
```

#### 3.3 各个加载方法添加缓存检查
```javascript
/** 获取单位列表 - 添加缓存机制 */
const loadUnits = async () => {
  // 如果已经加载过，直接返回
  if (unitList.value.length > 0) {
    return
  }
  
  // ... 请求逻辑
}

//库位信息相关方法 - 添加缓存机制
const initLocationList = async () => {
  // 如果已经加载过，直接返回
  if (locationList.value.length > 0) {
    return
  }
  
  // ... 请求逻辑
}

// 初始化仓库数据 - 添加缓存机制
const initWarehouseList = async () => {
  // 如果已经加载过，直接返回
  if (warehouseList.value.length > 0) {
    return
  }
  
  // ... 请求逻辑
}
```

### 4. 防抖机制优化

#### 4.1 工作订单页面 (`src/views/scm/mfg/workorder/index.vue`)
```javascript
// 添加防抖机制，避免重复点击
const isPickingFormOpening = ref(false)

const openPickingForm = async (type: string, row: WorkOrderVO) => {
  // 防止重复点击
  if (isPickingFormOpening.value) {
    return
  }
  
  try {
    isPickingFormOpening.value = true
    await pickingFormRef.value.open(type, null, row)
  } finally {
    // 延迟重置状态，避免快速连续点击
    setTimeout(() => {
      isPickingFormOpening.value = false
    }, 500)
  }
}
```

#### 4.2 领料页面 (`src/views/scm/inventory/pickingreceipt/index.vue`)
```javascript
/** 打开领料表单 - 添加防抖机制 */
const isPickingFormOpening = ref(false)

const openPickingForm = async (type: string, workOrder: WorkOrderVO) => {
  // 防止重复点击
  if (isPickingFormOpening.value) {
    return
  }
  
  try {
    isPickingFormOpening.value = true
    await pickingFormRef.value.open(type, null, workOrder)
  } finally {
    // 延迟重置状态，避免快速连续点击
    setTimeout(() => {
      isPickingFormOpening.value = false
    }, 500)
  }
}
```

## 优化效果

### 1. 请求数量减少
- **优化前：** 每次打开领料弹窗会发送 4-6 个请求
- **优化后：** 首次打开发送必要请求，后续打开复用缓存数据，请求数量减少 60-80%

### 2. 加载速度提升
- 缓存机制避免了重复的网络请求
- 并行加载基础数据，提升初始化速度
- 防抖机制避免了用户误操作导致的重复请求

### 3. 用户体验改善
- 弹窗打开速度更快
- 避免了因重复请求导致的界面卡顿
- 防止用户快速点击导致的异常行为

## 注意事项

1. **数据一致性：** 缓存机制可能导致数据不是最新的，如果需要实时数据，可以考虑添加刷新机制
2. **内存使用：** 缓存数据会占用一定内存，对于大量数据需要考虑内存管理
3. **会话管理：** 当前缓存是基于组件生命周期的，页面刷新后会重新加载数据

## 建议

1. 可以考虑将缓存机制提升到全局状态管理（如 Pinia）中，实现跨组件的数据共享
2. 添加数据过期机制，定期刷新缓存数据
3. 对于频繁变化的数据，可以考虑使用 WebSocket 或轮询机制保持数据同步
