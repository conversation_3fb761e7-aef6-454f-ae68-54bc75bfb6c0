# 领料弹窗请求优化说明

## 问题分析

在打开领料弹窗时，会同时发送多次重复的请求，主要原因包括：

### 1. 主表单重复请求
- `PickingReceiptForm.vue` 的 `open` 方法每次都会调用 `initWorkOrderList()` 获取工作订单列表
- 即使从工作订单页面点击"领料"按钮时，已经传入了工单数据，仍然会重复请求工单列表

### 2. 子表单组件重复初始化
- `PickingReceiptDetailForm.vue` 在 `onMounted` 时会并发调用多个初始化方法：
  - `initLocationList()` - 获取库位数据
  - `loadUnits()` - 获取单位数据
  - `initWarehouseList()` - 获取仓库数据
- 这些请求每次打开弹窗都会重新执行，且两个子表单组件（原料和包材）会各自独立请求，导致重复

### 3. 用户快速点击导致重复请求
- 用户快速连续点击"领料"按钮时，会触发多次弹窗打开，导致重复请求

## 优化方案

### 1. 父组件统一管理基础数据 (`PickingReceiptForm.vue`)

**核心思路：** 在父组件中统一加载基础数据，然后通过 props 传递给子组件，避免子组件重复请求。

#### 1.1 父组件中添加基础数据管理

```javascript
// 基础数据 - 在父组件中统一管理
const warehouseList = ref<any[]>([])
const locationList = ref<any[]>([])
const unitList = ref<any[]>([])

/** 打开弹窗 */
const open = async (type: string, id?: number, workOrderData?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 并行加载基础数据 - 在父组件中统一加载
  const loadPromises = []

  // 加载基础数据
  loadPromises.push(loadWarehouseList())
  loadPromises.push(loadLocationList())
  loadPromises.push(loadUnitList())

  // 优化：只在需要时才加载工作订单列表，避免重复请求
  if (!workOrderData && type === 'create') {
    loadPromises.push(initWorkOrderList())
  }

  // 并行执行所有加载任务
  await Promise.all(loadPromises)

  // ... 其他逻辑
}
```

#### 1.2 通过 props 传递给子组件

```vue
<PickingReceiptDetailForm
  ref="rawMaterialFormRef"
  :biz-order-id="formData.id"
  :warehouse-id="formData.warehouseId"
  :material-type="'raw'"
  :warehouse-list="warehouseList"
  :location-list="locationList"
  :unit-list="unitList"
/>
```

### 2. 子组件接收 props 并移除自己的加载逻辑 (`PickingReceiptDetailForm.vue`)

#### 2.1 修改 props 定义

```javascript
const props = defineProps({
  bizOrderId: {
    type: [String, Number],
    default: undefined
  },
  warehouseId: {
    type: [String, Number],
    default: undefined
  },
  materialType: {
    type: String,
    default: 'raw',
    validator: (value: string) => ['raw', 'package'].includes(value)
  },
  // 从父组件传入的基础数据
  warehouseList: {
    type: Array,
    default: () => []
  },
  locationList: {
    type: Array,
    default: () => []
  },
  unitList: {
    type: Array,
    default: () => []
  }
})
```

#### 2.2 移除子组件中的数据加载逻辑

```javascript
// 移除这些数据定义
// const warehouseList = ref<WarehouseVO[]>([])
// const locationList = ref<WarehouseLocationVO[]>([])
// const unitList = ref<any[]>([])

// 移除这些加载方法
// const initLocationList = async () => { ... }
// const loadUnits = async () => { ... }
// const initWarehouseList = async () => { ... }

//初始化方法 - 基础数据已从父组件传入，只需处理批次数据
onMounted(async () => {
  // 如果初始化时已有物料数据，加载对应的批次数据
  if (formData.value && formData.value.length > 0) {
    for (const row of formData.value) {
      if (row.materialId && !row.batchOptions) {
        row.batchOptions = []
        await loadBatchOptions(row, row.materialId)
      }
    }
  }
})
```

#### 2.3 修改数据引用

```javascript
// 原来的引用
// const unit = unitList.value.find(u => u.id === unitId)

// 修改为使用 props
const unit = props.unitList.find(u => u.id === unitId)

// 原来的引用
// return locationList.value.filter(location => { ... })

// 修改为使用 props
return props.locationList.filter(location => { ... })
```

### 4. 防抖机制优化

#### 4.1 工作订单页面 (`src/views/scm/mfg/workorder/index.vue`)
```javascript
// 添加防抖机制，避免重复点击
const isPickingFormOpening = ref(false)

const openPickingForm = async (type: string, row: WorkOrderVO) => {
  // 防止重复点击
  if (isPickingFormOpening.value) {
    return
  }
  
  try {
    isPickingFormOpening.value = true
    await pickingFormRef.value.open(type, null, row)
  } finally {
    // 延迟重置状态，避免快速连续点击
    setTimeout(() => {
      isPickingFormOpening.value = false
    }, 500)
  }
}
```

#### 4.2 领料页面 (`src/views/scm/inventory/pickingreceipt/index.vue`)
```javascript
/** 打开领料表单 - 添加防抖机制 */
const isPickingFormOpening = ref(false)

const openPickingForm = async (type: string, workOrder: WorkOrderVO) => {
  // 防止重复点击
  if (isPickingFormOpening.value) {
    return
  }
  
  try {
    isPickingFormOpening.value = true
    await pickingFormRef.value.open(type, null, workOrder)
  } finally {
    // 延迟重置状态，避免快速连续点击
    setTimeout(() => {
      isPickingFormOpening.value = false
    }, 500)
  }
}
```

## 优化效果

### 1. 请求数量大幅减少

- **优化前：** 每次打开领料弹窗会发送多个重复请求：
  - 两个子表单组件（原料和包材）各自独立调用 `getWarehouseList`、`getWarehouseLocationPage`、`getUnitPage`
  - 总共会发送 6 个重复的基础数据请求
- **优化后：** 在父组件中统一加载基础数据，只发送 3 个请求，减少 50% 的请求数量

### 2. 加载速度提升

- 父组件并行加载所有基础数据，提升初始化速度
- 子组件直接使用传入的数据，无需等待网络请求
- 防抖机制避免了用户误操作导致的重复请求

### 3. 用户体验改善

- 弹窗打开速度更快
- 避免了因重复请求导致的界面卡顿
- 防止用户快速点击导致的异常行为
- 数据一致性更好，两个子表单使用相同的基础数据

## 注意事项

1. **数据一致性：** 缓存机制可能导致数据不是最新的，如果需要实时数据，可以考虑添加刷新机制
2. **内存使用：** 缓存数据会占用一定内存，对于大量数据需要考虑内存管理
3. **会话管理：** 当前缓存是基于组件生命周期的，页面刷新后会重新加载数据

## 建议

1. 可以考虑将缓存机制提升到全局状态管理（如 Pinia）中，实现跨组件的数据共享
2. 添加数据过期机制，定期刷新缓存数据
3. 对于频繁变化的数据，可以考虑使用 WebSocket 或轮询机制保持数据同步
