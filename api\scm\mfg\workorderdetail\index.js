import request from "../../../../utils/request";

// 查询投料单明细分页
export function getWorkOrderDetailPageApi(params) {
	return request({
		url: '/scm/mfg/work-order/work-order-detail/page',
		method: 'GET',
		params
	})
}

// 根据业务订单ID查询投料单明细列表
export function getWorkOrderDetailListByBizOrderIdApi(bizOrderId) {
	return request({
		url: '/scm/mfg/work-order/work-order-detail/list-by-biz-order-id?bizOrderId=' + bizOrderId,
		method: 'GET'
	})
}

// 查询投料单明细详情
export function getWorkOrderDetailApi(id) {
	return request({
		url: '/scm/mfg/work-order-detail/get?id=' + id,
		method: 'GET'
	})
}
