<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" :width="1000">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="auto"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单号" prop="orderNo">
            <el-input v-model="formData.orderNo" placeholder="保存时自动生成" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务类型" prop="bizType">
            <el-select v-model="formData.bizType" placeholder="请选择业务类型" :disabled="isFromWorkOrder">
              <el-option
                v-for="item in inventory_transaction_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="来源类型" prop="sourceType">
            <el-select v-model="formData.sourceType" placeholder="请选择来源类型">
              <el-option 
              v-for="item in scm_biz_type"
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="来源单ID" prop="sourceId">
            <el-input v-model="formData.sourceId" placeholder="请输入来源单ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="来源单" prop="sourceNo">
            <el-select
              v-model="formData.sourceNo"
              placeholder="请选择来源单"
              filterable
              clearable
              @change="handleWorkOrderChange"
            >
              <el-option
                v-for="workOrder in workOrderList"
                :key="workOrder.id"
                :label="`${workOrder.workNo} - ${workOrder.productName}`"
                :value="workOrder.workNo"
              >
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <span style="font-weight: 500;">{{ workOrder.workNo }}</span>
                  <span style="color: #909399; font-size: 12px;">{{ workOrder.productName }}</span>
                </div>
                <div style="color: #c0c4cc; font-size: 11px; margin-top: 2px;">
                  客户: {{ workOrder.customerName || '未知' }} | 状态: {{ getWorkOrderStatusText(workOrder.status) }}
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="交易对象ID" prop="objectId">
            <el-input v-model="formData.objectId" placeholder="请输入交易对象ID" />
          </el-form-item>
        </el-col> -->
<!--        <el-col :span="8">-->
<!--          <el-form-item label="交易对象名称" prop="objectName">-->
<!--            <el-input v-model="formData.objectName" placeholder="请输入交易对象名称" />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="8">-->
<!--          <el-form-item label="交易对象订单号" prop="objectOrderNo">-->
<!--            <el-input v-model="formData.objectOrderNo" placeholder="请输入交易对象订单号" />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="8">
          <el-form-item label="领料日期" prop="date">
            <el-date-picker
              v-model="formData.date"
              type="date"
              value-format="x"
              placeholder="选择领料日期"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="科目ID" prop="accountId">
            <el-input v-model="formData.accountId" placeholder="请输入科目ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="摘要" prop="note">
            <el-input v-model="formData.note" placeholder="请输入摘要" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="审批状态" prop="approveStatus">
            <el-radio-group v-model="formData.approveStatus">
              <el-radio 
              v-for="item in approve_status" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
              />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批单号" prop="approveNo">
            <el-input v-model="formData.approveNo" placeholder="请输入审批单号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批人ID" prop="approverId">
            <el-input v-model="formData.approverId" placeholder="请输入审批人ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批人" prop="approverName">
            <el-input v-model="formData.approverName" placeholder="请输入审批人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批时间" prop="approveDate">
            <el-date-picker
              v-model="formData.approveDate"
              type="date"
              value-format="x"
              placeholder="选择审批时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门ID" prop="deptId">
            <el-input v-model="formData.deptId" placeholder="请输入部门ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务员ID" prop="empId">
            <el-input v-model="formData.empId" placeholder="请输入业务员ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理员ID" prop="managerId">
            <el-input v-model="formData.managerId" placeholder="请输入管理员ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理员1ID" prop="manger1Id">
            <el-input v-model="formData.manger1Id" placeholder="请输入管理员1ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="记账ID" prop="accountantId">
            <el-input v-model="formData.accountantId" placeholder="请输入记账ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="检验员ID" prop="checkerId">
            <el-input v-model="formData.checkerId" placeholder="请输入检验员ID" />
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="原料出库明细" name="pickingReceiptDetail">
        <PickingReceiptDetailForm
          ref="rawMaterialFormRef"
          :biz-order-id="formData.id"
          :warehouse-id="formData.warehouseId"
          :material-type="'raw'"
        />
      </el-tab-pane>
      <el-tab-pane label="包材出库明细" name="packageDetail">
        <PickingReceiptDetailForm
          ref="packageMaterialFormRef"
          :biz-order-id="formData.id"
          :warehouse-id="formData.warehouseId"
          :material-type="'package'"
        />
      </el-tab-pane>
      </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { PickingReceiptApi, PickingReceiptVO } from '@/api/scm/inventory/pickingreceipt'
import PickingReceiptDetailForm from './components/PickingReceiptDetailForm.vue'
import { DICT_TYPE,getStrDictOptions,getDictLabel } from '@/utils/dict'
import { WorkOrderApi, WorkOrderVO } from '@/api/scm/mfg/workorder'
/** 领料出库 表单 */
defineOptions({ name: 'PickingReceiptForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const isFromWorkOrder = ref(false) // 是否来自工单（用于控制业务类型是否禁用）
const formData = ref({
  id: undefined as number | undefined,
  orderNo: undefined as string | undefined,
  bizType: undefined as string | undefined,
  sourceType: undefined as string | undefined,
  sourceId: undefined as number | undefined,
  sourceNo: undefined as string | undefined,
  objectId: undefined as number | undefined,
  objectName: undefined as string | undefined,
  objectOrderNo: undefined as string | undefined,
  date: undefined as Date | undefined,
  warehouseId: undefined as number | undefined,
  accountId: undefined as number | undefined,
  note: undefined as string | undefined,
  remark: undefined as string | undefined,
  approveStatus: undefined as string | undefined,
  approveNo: undefined as string | undefined,
  approverId: undefined as number | undefined,
  approverName: undefined as string | undefined,
  approveDate: undefined as Date | undefined,
  deptId: undefined as number | undefined,
  empId: undefined as number | undefined,
  managerId: undefined as number | undefined,
  manger1Id: undefined as number | undefined,
  accountantId: undefined as number | undefined,
  checkerId: undefined as number | undefined,
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('pickingReceiptDetail')
const rawMaterialFormRef = ref() // 原料出库明细表单引用
const packageMaterialFormRef = ref() // 包材出库明细表单引用
const workOrderList = ref<WorkOrderVO[]>([])
const scm_biz_type = getStrDictOptions(DICT_TYPE.SCM_BIZ_TYPE)
const inventory_transaction_type = getStrDictOptions(DICT_TYPE.INVENTORY_TRANSACTION_TYPE)
/** 打开弹窗 */
const open = async (type: string, id?: number, workOrderData?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 优化：只在需要时才加载工作订单列表，避免重复请求
  if (!workOrderData && type === 'create') {
    await initWorkOrderList()
  }

  // 如果传入了工单数据，自动填充表单
  if (workOrderData && type === 'create') {
    isFromWorkOrder.value = true
    // 设置业务类型为生产领料
    formData.value.bizType = 'mfg_issue'
    // 设置来源单信息
    formData.value.sourceId = workOrderData.id
    formData.value.sourceNo = workOrderData.workNo
    // 设置交易对象信息（工单可能没有直接的客户信息，使用工单编号）
    formData.value.objectOrderNo = workOrderData.workNo
    formData.value.sourceType = 'mfg_work_order'
    // 设置交易日期为当前日期
    formData.value.date = new Date()

    // 自动加载工单明细数据
    await loadWorkOrderDetails(workOrderData)
  } else {
    isFromWorkOrder.value = false
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await PickingReceiptApi.getPickingReceipt(id)
      formData.value = data

      // 等待明细表单组件加载完成后设置明细数据
      await nextTick()
      if (data.pickingReceiptDetails && data.pickingReceiptDetails.length > 0) {
        // 根据materialCode字段分离原料和包材数据
        // 当materialCode以"4"开头时为包材，否则为原料
        const rawMaterialDetails = data.pickingReceiptDetails.filter((item: any) => {
          const materialCode = item.materialCode || ''
          return !materialCode.startsWith('4')
        })
        const packageMaterialDetails = data.pickingReceiptDetails.filter((item: any) => {
          const materialCode = item.materialCode || ''
          return materialCode.startsWith('4')
        })

        // 设置原料明细数据
        if (rawMaterialDetails.length > 0 && rawMaterialFormRef.value?.setData) {
          await rawMaterialFormRef.value.setData(rawMaterialDetails)
        }

        // 设置包材明细数据
        if (packageMaterialDetails.length > 0 && packageMaterialFormRef.value?.setData) {
          await packageMaterialFormRef.value.setData(packageMaterialDetails)
        }
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    // 验证原料明细表单
    if (rawMaterialFormRef.value) {
      await rawMaterialFormRef.value.validate()
    }
    // 验证包材明细表单
    if (packageMaterialFormRef.value) {
      await packageMaterialFormRef.value.validate()
    }
  } catch (e) {
    subTabsName.value = 'pickingReceiptDetail'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as PickingReceiptVO
    // 拼接子表的数据
    const rawMaterialData = rawMaterialFormRef.value ? rawMaterialFormRef.value.getData() : []
    const packageMaterialData = packageMaterialFormRef.value ? packageMaterialFormRef.value.getData() : []
    data.pickingReceiptDetails = [...rawMaterialData, ...packageMaterialData]
    if (formType.value === 'create') {
      await PickingReceiptApi.createPickingReceipt(data)
      message.success(t('common.createSuccess'))
    } else {
      await PickingReceiptApi.updatePickingReceipt(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

//初始化工作订单数据 - 添加缓存机制
const initWorkOrderList = async () => {
  // 如果已经加载过工作订单列表，直接返回
  if (workOrderList.value.length > 0) {
    return
  }

  try {
    // 获取已审批状态的字典值
    const approveStatusOptions = getStrDictOptions(DICT_TYPE.APPROVE_STATUS)
    const approvedStatus = approveStatusOptions.find(option => option.label === '已审批')?.value || '1'

    const res = await WorkOrderApi.getWorkOrderPage({
      pageNo: 1,
      pageSize: 100,
      status: approvedStatus // 只获取已审批的工作订单
    })
    workOrderList.value = res.list || []
  } catch (error) {
    workOrderList.value = []
  }
}

/** 加载工单明细并添加到领料明细中 */
const loadWorkOrderDetails = async (workOrderData: any) => {
  try {
    let detailList = []
    detailList = await WorkOrderApi.getWorkOrderDetailListByBizOrderId(workOrderData.id)
    if (detailList && detailList.length > 0) {

      // 将投料单明细转换为领料明细格式
      const pickingDetails = detailList.map((detail: any, index: number) => ({
        id: undefined, // 新增时ID为空
        num: index + 1, // 序号
        bizOrderId: formData.value.id, // 领料单ID
        bizOrderNo: formData.value.orderNo || '', // 领料单号
        // 优先使用投料单明细中的仓库ID，如果没有则使用表单中的仓库ID
        warehouseId: detail.warehouseId || formData.value.warehouseId, // 仓库ID
        locationId: detail.locationId || undefined, // 库位ID
        materialId: detail.materialId, // 物料ID
        materialName: detail.materialName, // 物料名称
        materialCode: detail.materialCode, // 物料编号
        unit: detail.unit, // 单位
        unitPrice: detail.unitPrice || 0, // 单价
        amount: detail.amount || 0, // 金额
        remark: detail.remark || '', // 明细备注
        // 优先使用投料单的计划数量，如果没有则使用quantity
        plannedQuantity: detail.plannedQuantity || detail.quantity || 0, // 计划数量
        fulfilledQuantity: detail.plannedQuantity || detail.quantity || 0, // 实际数量（默认等于计划数量）
        standardPlannedQuantity: detail.standardPlannedQuantity || 0, // 基本单位计划数量
        standardFulfilledQuantity: detail.standardPlannedQuantity || 0, // 基本单位实际数量
        standardUnit: detail.standardUnit || '', // 基本单位
        taxPrice: detail.taxPrice || 0, // 含税单价
        taxAmount: detail.taxAmount || 0, // 含税金额
        invoiceQuantity: 0, // 开票数量
        invoiceAmount: 0, // 开票金额
        standardInvoiceQuantity: 0, // 开票基本数量
        effictiveDate: undefined, // 生产日期
        expiryDate: undefined, // 失效日期
        note: detail.note || '', // 说明
        sourceId: detail.id, // 源单ID（投料单明细ID）
        sourceNo: workOrderData.workNo, // 源单单号（工单编号）
        batchNo: detail.batchNo || '', // 批号
        costObjectId: undefined, // 成本对象编码
        costObjectName: undefined, // 成本对象名称
        accountingVoucherNumber: undefined, // 记账凭证号
      }))

      // 设置明细数据到子表单
      await nextTick() // 等待DOM更新

      // 根据materialCode字段分离原料和包材数据
      // 当materialCode以"4"开头时为包材，否则为原料
      const rawMaterialDetails = pickingDetails.filter((item: any) => {
        const materialCode = item.materialCode || ''
        // 不是以"4"开头的为原料
        return !materialCode.startsWith('4')
      })

      const packageMaterialDetails = pickingDetails.filter((item: any) => {
        const materialCode = item.materialCode || ''
        // 以"4"开头的为包材
        return materialCode.startsWith('4')
      })



      // 设置原料明细数据
      if (rawMaterialDetails.length > 0 && rawMaterialFormRef.value) {
        await rawMaterialFormRef.value.setData(rawMaterialDetails)
      }

      // 设置包材明细数据
      if (packageMaterialDetails.length > 0 && packageMaterialFormRef.value) {
        await packageMaterialFormRef.value.setData(packageMaterialDetails)
      }
    } 
  } catch (error: any) {
    message.error('加载投料单明细失败: ' + (error?.message || error))
  }
}

// 处理工作订单选择变化
const handleWorkOrderChange = (workNo: string) => {
  if (!workNo) {
    // 清空相关字段
    formData.value.objectName = undefined
    formData.value.objectOrderNo = undefined
    return
  }

  // 根据选择的工作订单编号找到对应的工作订单
  const selectedWorkOrder = workOrderList.value.find(order => order.workNo === workNo)
  if (selectedWorkOrder) {
    // 自动填充相关字段
    formData.value.sourceId = selectedWorkOrder.id
    formData.value.objectName = selectedWorkOrder.customerName || ''
    formData.value.objectOrderNo = selectedWorkOrder.orderNo || ''
  }
}

// 获取工作订单状态文本
const getWorkOrderStatusText = (status: string | number) => {
  return getDictLabel(DICT_TYPE.APPROVE_STATUS, status) || '未知状态'
}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined as number | undefined,
    orderNo: undefined as string | undefined,
    bizType: undefined as string | undefined,
    sourceType: undefined as string | undefined,
    sourceId: undefined as number | undefined,
    sourceNo: undefined as string | undefined,
    objectId: undefined as number | undefined,
    objectName: undefined as string | undefined,
    objectOrderNo: undefined as string | undefined,
    date: undefined as Date | undefined,
    warehouseId: undefined as number | undefined,
    accountId: undefined as number | undefined,
    note: undefined as string | undefined,
    remark: undefined as string | undefined,
    approveStatus: undefined as string | undefined,
    approveNo: undefined as string | undefined,
    approverId: undefined as number | undefined,
    approverName: undefined as string | undefined,
    approveDate: undefined as Date | undefined,
    deptId: undefined as number | undefined,
    empId: undefined as number | undefined,
    managerId: undefined as number | undefined,
    manger1Id: undefined as number | undefined,
    accountantId: undefined as number | undefined,
    checkerId: undefined as number | undefined,
  }
  formRef.value?.resetFields()
}
</script>
