<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px" border max-height="300px" show-summary :summary-method="summaryMethod">
      <!-- <el-table-column label="序号" type="index" width="100" /> -->
      <el-table-column label="序号" min-width="60" prop="num" align="center"/>

      <el-table-column label="物料信息" min-width="300">
        <template #default="{ row }">
          <div class="material-source-info">
            <div class="material-section">
              <div class="material-name">
                <span class="label">名称：</span>
                <span class="value">{{ row.materialName || '-' }}</span>
              </div>
              <div class="material-details">
                <span class="label">编码：</span>
                <span class="value">{{ row.materialCode || '-' }}</span>
                <span class="separator">|</span>
                <span class="label">规格：</span>
                <span class="value">{{ row.materialSpec || '-' }}</span>
              </div>
            </div>
            <div class="source-section">
              <span class="label">源单：</span>
              <span class="value">{{ row.sourceNo || '-' }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="物料编号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialCode`" :rules="formRules.materialCode" class="mb-0px!">
            <el-input v-model="row.materialCode" placeholder="请输入物料编号" />
          </el-form-item>
        </template>
      </el-table-column> -->
<!--      <el-table-column label="物料规格" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.materialSpec`" :rules="formRules.materialSpec" class="mb-0px!">-->
<!--            <el-input v-model="row.materialSpec" placeholder="请输入物料规格" disabled/>-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="单位" min-width="100">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unit`" :rules="formRules.unit" class="mb-0px!">
            <el-select
              v-model="row.unit"
              placeholder="请选择单位"
              clearable
              class="!w-130px"
              disabled
            >
              <el-option
                v-for="unit in unitList"
                :key="unit.id"
                :label="unit.name"
                :value="unit.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
<!--      <el-table-column label="单价" min-width="150" prop="unitPrice">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.unitPrice`" :rules="formRules.unitPrice" class="mb-0px!">-->
<!--            <el-input-->
<!--              v-model="row.unitPrice"-->
<!--              placeholder="请输入单价"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="金额" min-width="150" prop="amount">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.amount`" :rules="formRules.amount" class="mb-0px!">-->
<!--            <el-input-->
<!--              v-model="row.amount"-->
<!--              placeholder="请输入金额"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->

      <el-table-column label="仓库" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.warehouseId`" :rules="formRules.warehouseId" class="mb-0px!">
            <el-tree-select
              v-model="row.warehouseId"
              :data="warehouseTreeData"
              placeholder="请选择仓库"
              clearable
              filterable
              check-strictly
              :render-after-expand="false"
              class="!w-130px"
              node-key="id"
              :props="{
                value: 'id',
                label: 'name',
                children: 'children',
                disabled: 'disabled'
              }"
              @change="(value) => handleWarehouseChange($index, value)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="应领数量" min-width="150" prop="plannedQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.plannedQuantity`" :rules="formRules.plannedQuantity" class="mb-0px!">
            <el-input
              v-model="row.plannedQuantity"
              placeholder="请输入应领数量"
              disabled
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="实领数量" min-width="150" prop="fulfilledQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.fulfilledQuantity`" :rules="formRules.fulfilledQuantity" class="mb-0px!">
            <el-input
              v-model="row.fulfilledQuantity"
              placeholder="请输入实领数量"
            />
          </el-form-item>
        </template>
      </el-table-column>
<!--      <el-table-column label="基本单位应领数量" min-width="150" prop="standardPlannedQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardPlannedQuantity`" :rules="formRules.standardPlannedQuantity" class="mb-0px!">
            <el-input
              v-model="row.standardPlannedQuantity"
              placeholder="请输入基本单位应领数量"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="基本单位实领数量" min-width="150" prop="standardFulfilledQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardFulfilledQuantity`" :rules="formRules.standardFulfilledQuantity" class="mb-0px!">
            <el-input
              v-model="row.standardFulfilledQuantity"
              placeholder="请输入基本单位实领数量"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="基本单位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardUnit`" :rules="formRules.standardUnit" class="mb-0px!">
            <el-select
              v-model="row.standardUnit"
              placeholder="请选择基本单位"
              clearable
              class="!w-130px"
            >
              <el-option
                v-for="unit in unitList"
                :key="unit.id"
                :label="unit.name"
                :value="unit.name"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="含税单价" min-width="150" prop="taxPrice">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxPrice`" :rules="formRules.taxPrice" class="mb-0px!">
            <el-input
              v-model="row.taxPrice"
              placeholder="请输入含税单价"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="含税金额" min-width="150" prop="taxAmount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxAmount`" :rules="formRules.taxAmount" class="mb-0px!">
            <el-input
              v-model="row.taxAmount"
              placeholder="请输入含税金额"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开票数量" min-width="150" prop="invoiceQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.invoiceQuantity`" :rules="formRules.invoiceQuantity" class="mb-0px!">
            <el-input
              v-model="row.invoiceQuantity"
              placeholder="请输入开票数量"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开票金额" min-width="150" prop="invoiceAmount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.invoiceAmount`" :rules="formRules.invoiceAmount" class="mb-0px!">
            <el-input
              v-model="row.invoiceAmount"
              placeholder="请输入开票金额"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开票基本数量" min-width="150" prop="standardInvoiceQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardInvoiceQuantity`" :rules="formRules.standardInvoiceQuantity" class="mb-0px!">
            <el-input
              v-model="row.standardInvoiceQuantity"
              placeholder="请输入开票基本数量"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="生产日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.effictiveDate`" :rules="formRules.effictiveDate" class="mb-0px!">
            <el-date-picker
              v-model="row.effictiveDate"
              type="date"
              value-format="x"
              placeholder="选择生产日期"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="失效日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.expiryDate`" :rules="formRules.expiryDate" class="mb-0px!">
            <el-date-picker
              v-model="row.expiryDate"
              type="date"
              value-format="x"
              placeholder="选择失效日期"
            />
          </el-form-item>
        </template>
      </el-table-column>-->
      <el-table-column label="说明" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.note`" :rules="formRules.note" class="mb-0px!">
            <el-input v-model="row.note" placeholder="请输入说明" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
     <el-table-column label="批号" min-width="200">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.batchNo`" :rules="formRules.batchNo" class="mb-0px!">
            <el-select
              v-model="row.batchNo"
              placeholder="请选择批号"
              clearable
              filterable
              class="!w-180px"
              :disabled="!row.materialId"
            >
              <el-option
                v-for="batch in row.batchOptions || []"
                :key="batch.id"
                :label="formatBatchLabel(batch)"
                :value="batch.batchNo"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="成本对象编码" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.costObjectId`" :rules="formRules.costObjectId" class="mb-0px!">
            <el-input v-model="row.costObjectId" placeholder="请输入成本对象编码" disabled/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="成本对象名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.costObjectName`" :rules="formRules.costObjectName" class="mb-0px!">
            <el-input v-model="row.costObjectName" placeholder="请输入成本对象名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="记账凭证号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.accountingVoucherNumber`" :rules="formRules.accountingVoucherNumber" class="mb-0px!">
            <el-input v-model="row.accountingVoucherNumber" placeholder="请输入记账凭证号" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="库位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.locationId`" :rules="formRules.locationId" class="mb-0px!">
            <el-select
              v-model="row.locationId"
              placeholder="请选择库位"
              clearable
              filterable
              class="!w-130px"
            >
              <el-option
                v-for="location in getFilteredLocationList(row.warehouseId)"
                :key="location.id"
                :label="location.name"
                :value="location.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <!-- <el-button @click="handleDelete($index)" link>—</el-button> -->
           <Icon icon="ep:delete" @click="handleDelete($index)" color="#f56c6c"/>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加领料出库明细</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { PickingReceiptApi } from '@/api/scm/inventory/pickingreceipt'
import { InventoryDetail } from '@/types/inventory'
import { WarehouseLocationApi,WarehouseLocationVO } from '@/api/scm/inventory/warehouselocation';
import { WarehouseApi, WarehouseVO } from '@/api/scm/inventory/warehouse';
import { MaterialApi, MaterialVO } from '@/api/scm/base/material';
import { UnitApi } from '@/api/scm/base/unit';
import { BatchInfoApi } from '@/api/scm/inventory/batchinfo'
import { getRemoteUnit } from '@/utils/commonBiz';
import { formatAmount, formatQuantity } from '@/utils/formatter';
import { handleTree } from '@/utils/tree';
import ScrollSelect from '@/components/ScrollSelect/index.vue';


const props = defineProps({
  bizOrderId: {
    type: [String, Number],
    default: undefined
  },
  warehouseId: {
    type: [String, Number],
    default: undefined
  },
  materialType: {
    type: String,
    default: 'raw', // 'raw' 原料, 'package' 包材
    validator: (value: string) => ['raw', 'package'].includes(value)
  },

})
const formLoading = ref(false) // 表单的加载中
const formData = ref<InventoryDetail[]>([])
const formRules = reactive<any>({
})


const formRef = ref() // 表单 Ref
const locationList = ref<WarehouseLocationVO[]>([])
const unitList = ref<any[]>([]) // 单位列表数据
const warehouseList = ref<WarehouseVO[]>([]) // 仓库列表数据
const warehouseTreeData = ref<any[]>([]) // 仓库树形数据

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.bizOrderId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      const details = await PickingReceiptApi.getPickingReceiptDetailListByBizOrderId(val)
      
      // 为每行添加批次选项字段并加载批次数据
      for (const row of details) {
        row.batchOptions = []
        if (row.materialId) {
          await loadBatchOptions(row, row.materialId)
        }
      }
      
      formData.value = details
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)
watch(
  () => props.warehouseId,
  async (val) => {
    formData.value = formData.value.map((item:InventoryDetail) => {
      item.warehouseId = val
      return item
    })
  }
)

/** 格式化物料选项显示标签 */
const formatMaterialLabel = (material: MaterialVO) => {
  if (!material) return ''
  const fullCode = material.fullCode || ''
  const name = material.name || ''
  const spec = material.spec || ''

  // 构建显示标签，只有非空值才参与拼接
  const parts: string[] = []
  if (fullCode) parts.push(fullCode)
  if (name) parts.push(name)
  if (spec) parts.push(spec)

  return parts.join(' - ')
}

/** 获取单位列表 - 添加缓存机制 */
const loadUnits = async () => {
  // 如果已经加载过，直接返回
  if (unitList.value.length > 0) {
    return
  }

  try {
    unitList.value = await getRemoteUnit()
  } catch (error) {
    console.error('获取单位数据失败:', error)
    unitList.value = []
  }
}

/** 加载批次选项 */
const loadBatchOptions = async (row: any, materialId: string | number) => {
  if (!materialId) {
    row.batchOptions = []
    row.batchNo = undefined
    return
  }

  try {
    const response = await BatchInfoApi.getSimpleBatchInfoListByMaterialId({
      materialId: Number(materialId)
    })
    
    const batchList = response || []
    row.batchOptions = batchList
    
    // 默认选中第一个批次
    if (batchList.length > 0) {
      row.batchNo = batchList[0].batchNo
    } else {
      row.batchNo = undefined
    }
  } catch (error) {
    row.batchOptions = []
    row.batchNo = undefined
  }
}

/** 格式化批次显示标签 */
const formatBatchLabel = (batch: any) => {
  if (!batch) return ''
  return `${batch.batchNo} (数量:${batch.quantity}, 未锁数量:${batch.unlockQuantity})`
}

// 添加缓存标志，避免重复请求
const isLocationListLoaded = ref(false)
const isUnitsLoaded = ref(false)
const isWarehouseListLoaded = ref(false)

//初始化方法 - 优化为按需加载
onMounted(async () => {
  // 并行加载基础数据，但添加缓存机制避免重复请求
  const initPromises = []

  if (!isLocationListLoaded.value) {
    initPromises.push(initLocationList().then(() => { isLocationListLoaded.value = true }))
  }

  if (!isUnitsLoaded.value) {
    initPromises.push(loadUnits().then(() => { isUnitsLoaded.value = true }))
  }

  if (!isWarehouseListLoaded.value) {
    initPromises.push(initWarehouseList().then(() => { isWarehouseListLoaded.value = true }))
  }

  // 等待所有基础数据加载完成
  await Promise.all(initPromises)

  // 如果初始化时已有物料数据，加载对应的批次数据
  if (formData.value && formData.value.length > 0) {
    for (const row of formData.value) {
      if (row.materialId && !row.batchOptions) {
        row.batchOptions = []
        await loadBatchOptions(row, row.materialId)
      }
    }
  }
})

/** 新增按钮操作 */
const handleAdd = () => {
  // 根据物料类型设置默认仓库ID
  let defaultWarehouseId = props.warehouseId

  const row:InventoryDetail = {
    id: undefined,
    num: undefined,
    bizOrderId: undefined,
    bizOrderNo: undefined,
    warehouseId: defaultWarehouseId,
    locationId: undefined,
    materialId: undefined,
    materialName: undefined,
    materialCode: undefined,
    materialSpec: undefined,
    unit: undefined,
    unitPrice: undefined,
    amount: undefined,
    remark: undefined,
    plannedQuantity: undefined,
    fulfilledQuantity: undefined,
    standardPlannedQuantity: undefined,
    standardFulfilledQuantity: undefined,
    standardUnit: undefined,
    taxPrice: undefined,
    taxAmount: undefined,
    invoiceQuantity: undefined,
    invoiceAmount: undefined,
    standardInvoiceQuantity: undefined,
    effictiveDate: undefined,
    expiryDate: undefined,
    note: undefined,
    sourceId: undefined,
    sourceNo: undefined,
    batchNo: undefined,
    batchOptions: [], // 添加批次选项
    costObjectId: undefined,
    costObjectName: undefined,
    accountingVoucherNumber: undefined,
  }
  row.warehouseId = props.warehouseId
  row.bizOrderId = props.bizOrderId
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

//库位信息相关方法 - 添加缓存机制
const initLocationList = async () => {
  // 如果已经加载过，直接返回
  if (locationList.value.length > 0) {
    return
  }

  try {
    const res = await WarehouseLocationApi.getWarehouseLocationPage({
      pageNo: 1,
      pageSize: 100 // 增加页面大小以获取更多库位
    })

    // 处理分页数据结构
    if (res && res.list) {
      locationList.value = res.list
    } else if (Array.isArray(res)) {
      locationList.value = res
    } else {
      locationList.value = []
    }
  } catch (error) {
    console.error('加载库位数据失败:', error)
    locationList.value = []
  }
}

/** 表单值 */
const getData = () => {
  // 过滤掉batchOptions字段，避免传递给后端
  return formData.value.map(item => {
    const { batchOptions, ...rest } = item
    return rest
  })
}

// 加载物料的方法，用于ScrollSelect组件
const loadMaterials = async (params: any) => {
  try {
    let res = await MaterialApi.getMaterialPage(params)
    return res
  } catch (error) {
    return { list: [], total: 0 }
  }
}
// 处理物料选择变化
const handleMaterialChange = async (index: number, data: any) => {
  if (!data) {
    // 清空物料相关字段
    formData.value[index].materialId = undefined
    formData.value[index].materialName = undefined
    formData.value[index].materialCode = undefined
    formData.value[index].materialSpec = undefined
    formData.value[index].unit = undefined
    formData.value[index].unitName = undefined
    formData.value[index].batchNo = undefined
    formData.value[index].batchOptions = []
    return
  }

  // 获取选中的物料详情
  const selectedMaterialId = data
  if (selectedMaterialId) {
    const selectedMaterial = await MaterialApi.getMaterial(selectedMaterialId)
    // 更新物料相关信息
    formData.value[index].materialId = selectedMaterial.id
    formData.value[index].materialName = selectedMaterial.name
    formData.value[index].materialCode = selectedMaterial.fullCode
    formData.value[index].materialSpec = selectedMaterial.spec
    if(selectedMaterial.unit){
      // 确保单位ID是数字类型，与选择器的value类型一致
      const unitId = Number(selectedMaterial.unit)
      formData.value[index].unit = unitId

      // 从单位列表中查找对应的单位信息
      const unit = unitList.value.find(u => u.id === unitId)
      if (unit) {
        formData.value[index].unitName = unit.name
      } else {
        // 如果在当前单位列表中找不到，则从API获取
        try {
          const unitDetail = await UnitApi.getUnit(unitId)
          formData.value[index].unitName = unitDetail.name
        } catch (error) {
          console.error('获取单位详情失败:', error)
        }
      }
    } else {
      // 清空单位信息
      formData.value[index].unit = undefined
      formData.value[index].unitName = undefined
    }
    
    // 获取批次数据
    await loadBatchOptions(formData.value[index], selectedMaterial.id)
  }
}

/** 设置表单数据 */
const setData = async (data: any[]) => {
  if (!data || data.length === 0) {
    formData.value = []
    return
  }

  // 根据materialType和materialCode过滤数据
  let filteredData = data
  if (props.materialType === 'raw') {
    // 原料：materialCode不以"4"开头
    filteredData = data.filter(item => {
      const materialCode = item.materialCode || ''
      return !materialCode.startsWith('4')
    })
  } else if (props.materialType === 'package') {
    // 包材：materialCode以"4"开头
    filteredData = data.filter(item => {
      const materialCode = item.materialCode || ''
      return materialCode.startsWith('4')
    })
  }

  // 确保单位数据已加载
  if (unitList.value.length === 0) {
    await loadUnits()
  }

  // 处理每一行数据，转换单位信息
  const processedData = await Promise.all(filteredData.map(async (item) => {
    const processedItem = { ...item }

    // 如果 unit 字段存在且是数字（单位ID），需要确保选择器能正确显示
    if (processedItem.unit) {
      const unitId = typeof processedItem.unit === 'string' ? parseInt(processedItem.unit) : processedItem.unit

      // 在单位列表中查找对应的单位
      const foundUnit = unitList.value.find(u => u.id === unitId)
      if (foundUnit) {
        processedItem.unit = foundUnit.id // 保持单位ID，与选择器的value类型一致
        processedItem.unitName = foundUnit.name
      } else {
        // 如果在本地列表中找不到，尝试从API获取
        try {
          const unitInfo = await UnitApi.getUnit(unitId)
          if (unitInfo) {
            processedItem.unit = unitInfo.id // 保持单位ID
            processedItem.unitName = unitInfo.name
          }
        } catch (error) {
          console.error(`获取单位信息失败:`, error)
        }
      }
    }

    // 初始化批次选项并加载批次数据
    processedItem.batchOptions = []
    if (processedItem.materialId) {
      await loadBatchOptions(processedItem, processedItem.materialId)
    }

    return processedItem
  }))

  formData.value = processedData
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = ['plannedQuantity', 'fulfilledQuantity', 'standardPlannedQuantity',
                           'standardFulfilledQuantity', 'invoiceQuantity', 'standardInvoiceQuantity']

    // 需要汇总的金额字段
    const amountFields = ['unitPrice', 'amount', 'taxPrice', 'taxAmount', 'invoiceAmount']

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatQuantity(total)
    } else if (amountFields.includes(column.property)) {
      // 金额字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatAmount(total)
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

// 初始化仓库数据 - 添加缓存机制
const initWarehouseList = async () => {
  // 如果已经加载过，直接返回
  if (warehouseList.value.length > 0) {
    return
  }

  try {
    // 尝试多种方式获取仓库数据
    let warehouses: any[] = []

    try {
      const response1 = await WarehouseApi.getWarehouseList({ pageNo: 1, pageSize: 100 })

      if (Array.isArray(response1)) {
        warehouses = response1
      } else if (response1 && response1.list) {
        warehouses = response1.list
      } else if (response1 && response1.data) {
        warehouses = response1.data
      }
    } catch (error1) {
      try {
        const response2 = await WarehouseApi.getWarehouseList({})

        if (Array.isArray(response2)) {
          warehouses = response2
        } else if (response2 && response2.list) {
          warehouses = response2.list
        } else if (response2 && response2.data) {
          warehouses = response2.data
        }
      } catch (error2) {
        console.error('获取仓库数据失败:', error2)
      }
    }

    warehouseList.value = warehouses
    // 转换为树形结构
    warehouseTreeData.value = handleTree(warehouseList.value, 'id', 'parentId')
  } catch (error) {
    console.error('获取仓库数据失败:', error)
    warehouseList.value = []
    warehouseTreeData.value = []
  }
}



// 根据仓库ID过滤库位列表
const getFilteredLocationList = (warehouseId: any) => {
  // 确保 locationList.value 是数组
  if (!Array.isArray(locationList.value)) {
    console.warn('locationList.value 不是数组:', locationList.value)
    return []
  }

  if (!warehouseId) {
    return locationList.value
  }

  // 确保每个 location 对象都有 warehouseId 属性
  return locationList.value.filter(location => {
    if (!location || typeof location !== 'object') {
      return false
    }
    return location.warehouseId === warehouseId
  })
}

// 处理仓库选择变化
const handleWarehouseChange = (index: number, warehouseId: any) => {
  // 清空库位选择
  formData.value[index].locationId = undefined

  // 如果主表没有选择仓库，则更新主表仓库
  if (warehouseId && !props.warehouseId) {
    // 这里可以触发事件通知父组件更新主表仓库
    // emit('warehouse-change', warehouseId)
  }
}

defineExpose({ validate, getData, setData })
</script>

<style lang="scss" scoped>
.material-source-info {
  .material-section {
    margin-bottom: 6px;

    .material-details {
      display: flex;
      align-items: center;
      font-size: 12px;
      line-height: 1.4;

      .label {
        color: #909399;
        font-weight: 500;
        margin-right: 4px;
      }

      .value {
        color: #606266;
        margin-right: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 80px;
      }

      .separator {
        color: #dcdfe6;
        margin: 0 6px;
      }
    }
  }

  .source-section,
  .material-name{
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 1.4;

    .label {
      color: #909399;
      font-weight: 500;
      min-width: 36px;
      flex-shrink: 0;
    }

    .value {
      color: #606266;
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
