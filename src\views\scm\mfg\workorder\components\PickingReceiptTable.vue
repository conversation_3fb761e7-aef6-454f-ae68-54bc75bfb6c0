<template>
  <!-- 领料单表格 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="flattenedList"
      :stripe="true"
      border
      :show-overflow-tooltip="true"
      highlight-current-row
      show-summary
      :summary-method="summaryMethod"
      :span-method="objectSpanMethod"
      :max-height="flattenedList.length > 0 ? 600 : 200"
      style="width: 100%"
    >
      <!-- 最重要的主单据信息 -->
      <el-table-column label="单号" align="left" prop="orderNo" min-width="120px" fixed="left"/>
      <el-table-column label="业务类型" align="left" prop="bizType" min-width="100px" fixed="left">
        <template #default="scope">
          {{ getDictLabel(DICT_TYPE.INVENTORY_TRANSACTION_TYPE, scope.row.bizType) }}
        </template>
      </el-table-column>

      <!-- 核心明细信息 - 最重要的业务数据 -->
      <!-- <el-table-column label="序号" align="center" prop="detail.num" width="60px" /> -->
      <el-table-column label="物料名称" align="left" prop="detail.materialName" min-width="200px" fixed="left"/>
      <el-table-column label="物料编号" align="left" prop="detail.materialCode" min-width="140px" />
      <el-table-column label="实领数量" align="right" prop="detail.fulfilledQuantity" min-width="100px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.fulfilledQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="应领数量" align="right" prop="detail.plannedQuantity" min-width="100px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.plannedQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="单位" align="center" min-width="80px">
        <template #default="scope">
          {{ getUnitName(scope.row.detail?.unit) || scope.row.detail?.unit || '' }}
        </template>
      </el-table-column>
      <el-table-column label="单价" align="right" prop="detail.unitPrice" min-width="100px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.unitPrice) }}
        </template>
      </el-table-column>
      <el-table-column label="金额" align="right" prop="detail.amount" min-width="100px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.amount) }}
        </template>
      </el-table-column>
      <el-table-column label="批号" align="left" prop="detail.batchNo" min-width="120px"/>

      <!-- 重要的主单据业务信息 -->
      <el-table-column label="交易对象名称" align="left" prop="objectName" min-width="150px"/>
      <el-table-column
        label="交易日期"
        align="center"
        prop="date"
        :formatter="dateFormatter"
        min-width="110px"
      />
      <el-table-column label="审批状态" align="center" prop="approveStatus" min-width="100px">
        <template #default="scope">
          {{ getDictLabel('APPROVE_STATUS', scope.row.approveStatus) }}
        </template>
      </el-table-column>

      <!-- 税务和财务信息 -->
      <el-table-column label="含税单价" align="right" prop="detail.taxPrice" min-width="100px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.taxPrice) }}
        </template>
      </el-table-column>
      <el-table-column label="含税金额" align="right" prop="detail.taxAmount" min-width="110px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.taxAmount) }}
        </template>
      </el-table-column>

      <!-- 基本单位信息 -->
      <el-table-column label="基本单位" align="center" min-width="100px">
        <template #default="scope">
          {{ getUnitName(scope.row.detail?.standardUnit) || scope.row.detail?.standardUnit || '' }}
        </template>
      </el-table-column>
      <el-table-column label="基本单位实领数量" align="right" prop="detail.standardFulfilledQuantity" min-width="140px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.standardFulfilledQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="基本单位应领数量" align="right" prop="detail.standardPlannedQuantity" min-width="140px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.standardPlannedQuantity) }}
        </template>
      </el-table-column>

      <!-- 开票信息 -->
      <!-- <el-table-column label="开票数量" align="right" prop="detail.invoiceQuantity" min-width="100px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.invoiceQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="开票金额" align="right" prop="detail.invoiceAmount" min-width="100px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.invoiceAmount) }}
        </template>
      </el-table-column> -->

      <!-- 日期信息 -->
      <el-table-column
        label="生产日期"
        align="center"
        prop="detail.effictiveDate"
        :formatter="dateFormatter"
        min-width="110px"
      />
      <el-table-column
        label="失效日期"
        align="center"
        prop="detail.expiryDate"
        :formatter="dateFormatter"
        min-width="110px"
      />

      <!-- 其他主单据信息 -->
      <el-table-column label="摘要" align="left" prop="note" min-width="150px"/>
      <el-table-column label="备注" align="left" prop="remark" min-width="150px"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        min-width="160px"
      />

      <!-- 详细的明细信息 -->
      <el-table-column label="明细备注" align="left" prop="detail.remark" min-width="150px"/>
      <el-table-column label="说明" align="left" prop="detail.note" min-width="150px"/>
    </el-table>
  </ContentWrap>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { PickingReceiptApi } from '@/api/scm/inventory/pickingreceipt'
import { formatQuantity, formatAmount } from '@/utils/formatter'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { getRemoteUnit } from '@/utils/commonBiz'

const props = defineProps<{
  workOrderId?: number // 工单ID
}>()

const loading = ref(false) // 列表的加载中
const list = ref<any[]>([]) // 原始领料单数据
const flattenedList = ref<any[]>([]) // 扁平化后的数据（主单据+明细）
const unitMap = ref<Map<number, string>>(new Map()) // 单位ID到名称的映射
const spanArr = ref<number[]>([]) // 用于表格行合并

/** 加载单位数据 */
const loadUnits = async () => {
  try {
    const units = await getRemoteUnit()
    if (!units || units.length === 0) {
      return
    }
    units.forEach((unit: any) => {
      if (unit && unit.id && unit.name) {
        unitMap.value.set(unit.id, unit.name)
      }
    })
  } catch (error) {
    console.error('加载单位数据失败:', error)
  }
}

/** 根据单位ID获取单位名称 */
const getUnitName = (unitId: number | string): string => {
  if (!unitId) return ''
  const id = typeof unitId === 'string' ? parseInt(unitId) : unitId
  return unitMap.value.get(id) || ''
}

/** 加载领料单数据 */
const getList = async () => {
  if (!props.workOrderId) {
    console.warn('工单ID为空，无法加载领料单数据')
    return
  }

  loading.value = true
  try {
    // 直接使用工单ID查询领料单
    const pickingReceiptData = await PickingReceiptApi.getPickingReceiptPage({
      sourceId: props.workOrderId, // 使用工单ID作为来源单据ID查询
      pageNo: 1,
      pageSize: 100
    })

    // 确保返回的是数组格式
    if (pickingReceiptData && Array.isArray(pickingReceiptData.list)) {
      list.value = pickingReceiptData.list
      // 直接使用列表中的 details 字段，无需额外调用明细接口
      processData()
    } else {
      console.warn('领料单API返回数据格式不正确:', pickingReceiptData)
      list.value = []
      flattenedList.value = []
    }
  } catch (error) {
    console.error('领料单数据加载失败:', error)
    list.value = []
    flattenedList.value = []
  } finally {
    loading.value = false
  }
}

/** 处理数据：直接使用列表中的details字段并扁平化 */
const processData = () => {
  const flattened: any[] = []
  const spanArray: number[] = []

  for (const receipt of list.value) {
    // 直接使用领料单列表中的 details 字段
    const details = Array.isArray(receipt.details) ? receipt.details : []

    if (details.length === 0) {
      // 如果没有明细，显示主单据信息
      flattened.push({
        ...receipt,
        detail: {}
      })
      spanArray.push(1)
    } else {
      // 有明细的情况，每个明细一行
      details.forEach((detail, index) => {
        flattened.push({
          ...receipt,
          detail: detail
        })
        // 第一行显示主单据信息，其他行合并
        spanArray.push(index === 0 ? details.length : 0)
      })
    }
  }

  flattenedList.value = flattened
  spanArr.value = spanArray
}

/** 表格行合并方法 */
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 需要合并的主单据字段（移除了源单相关字段）
  const mergeFields = [
    'orderNo', 'bizType', 'objectName', 'date', 'approveStatus',
    'note', 'remark', 'createTime'
  ]

  if (mergeFields.includes(column.property)) {
    const span = spanArr.value[rowIndex]
    if (span > 0) {
      return {
        rowspan: span,
        colspan: 1
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }
  return {
    rowspan: 1,
    colspan: 1
  }
}

/** 表格汇总方法 */
const summaryMethod = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []

  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 数量字段汇总
    const quantityFields = [
      'detail.fulfilledQuantity', 'detail.plannedQuantity',
      'detail.standardFulfilledQuantity', 'detail.standardPlannedQuantity',
      'detail.invoiceQuantity', 'detail.standardInvoiceQuantity'
    ]

    // 金额字段汇总（包括单价和含税单价）
    const amountFields = [
      'detail.unitPrice', 'detail.amount', 'detail.taxPrice',
      'detail.taxAmount', 'detail.invoiceAmount'
    ]

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => {
        const value = column.property.split('.').reduce((obj, key) => obj?.[key], item)
        return Number(value) || 0
      })
      const total = values.reduce((prev, curr) => prev + curr, 0)
      if (total > 0) {
        sums[index] = formatQuantity(total)
      } else {
        sums[index] = ''
      }
    } else if (amountFields.includes(column.property)) {
      // 金额字段汇总（包括单价、金额、含税单价、含税金额）
      const values = data.map((item: any) => {
        const value = column.property.split('.').reduce((obj: any, key: string) => obj?.[key], item)
        return Number(value) || 0
      })
      const total = values.reduce((prev: number, curr: number) => prev + curr, 0)
      if (total > 0) {
        sums[index] = formatAmount(total)
      } else {
        sums[index] = ''
      }
    } else {
      sums[index] = ''
    }
  })

  return sums
}

/** 监听工单ID变化，重新加载数据 */
watch(
  () => props.workOrderId,
  async (newId) => {
    if (newId) {
      await loadUnits()
      await getList()
    } else {
      list.value = []
      flattenedList.value = []
    }
  },
  { immediate: true }
)

/** 暴露刷新方法 */
defineExpose({
  refresh: getList
})
</script>
