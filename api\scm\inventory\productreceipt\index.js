import request from "../../../../utils/request";

// 查询产品入库分页
export function getProductReceiptPageApi(params) {
	return request({
		url: '/scm/inventory/product-receipt/page',
		method: 'GET',
		params
	})
}

// 查询产品入库详情
export function getProductReceiptApi(id) {
	return request({
		url: '/scm/inventory/product-receipt/get?id=' + id,
		method: 'GET'
	})
}

// 根据业务订单ID查询产品入库明细列表
export function getProductReceiptDetailListByBizOrderIdApi(bizOrderId) {
	return request({
		url: '/scm/inventory/product-receipt/product-receipt-detail/list-by-biz-order-id?bizOrderId=' + bizOrderId,
		method: 'GET'
	})
}
